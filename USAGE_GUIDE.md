# Call of Duty: Black Ops 3 Mod Framework - Usage Guide

## ⚠️ CRITICAL WARNINGS

**READ THIS FIRST - EXTREMELY IMPORTANT**

- **EDUCATIONAL PURPOSE ONLY**: This framework is designed for learning reverse engineering and game modding concepts
- **OFFLINE USE ONLY**: Never use this in online multiplayer - you WILL be banned
- **ANTI-CHEAT DETECTION**: Modern anti-cheat systems will detect and ban accounts using these techniques
- **LEGAL RESPONSIBILITY**: You are solely responsible for how you use this framework
- **GAME VERSION**: This framework is specifically for Call of Duty: Black Ops 3 version ********

## 🎯 Quick Start Guide

### Step 1: Verify Your Setup

1. **Check Game Version**:
   - Launch Black Ops 3
   - Go to Settings → Game Options
   - Verify version is ********
   - If different version, memory addresses will NOT work

2. **Validate Framework**:
   ```bash
   cd BLOPS3/build
   python validate_framework.py
   ```
   - Should show 100% completion rate
   - If not, check the validation report for issues

### Step 2: Build the Framework

**Option A: Using Visual Studio (Recommended)**
```bash
# Open Visual Studio Developer Command Prompt
cd BLOPS3
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022" -A x64
cmake --build . --config Release
```

**Option B: Using MinGW**
```bash
cd BLOPS3
make all
```

**Option C: Using Build Script**
```bash
cd BLOPS3
build.bat
```

### Step 3: Test the Build

```bash
# Run the test executable
cd build/bin
BO3BuildTest.exe
```

Expected output:
```
BO3 Mod Framework Build Test
Testing basic functionality...
Vec3 test: (1, 2, 3)
Distance test: 5.0 (expected: 5.0)
Build test completed successfully!
```

### Step 4: Deploy the Mod

1. **Start Black Ops 3** (in offline mode)

2. **Run the Injector**:
   ```bash
   cd build/bin
   BO3Injector.exe BO3ExampleMod.dll
   ```

3. **Control the Mod**:
   - **F1**: Toggle ESP (Enemy/Player highlighting)
   - **F2**: Toggle Aimbot assistance
   - **F3**: Toggle Information Display
   - **ESC**: Exit injector

## 🔧 Advanced Usage

### Creating Custom Mods

1. **Copy the Example Mod**:
   ```cpp
   // Create MyCustomMod.cpp
   #include "BO3_ModFramework.h"
   
   class MyCustomMod {
   public:
       void Update() {
           if (!IsInGame()) return;
           
           // Your custom mod code here
           DrawCustomFeatures();
       }
       
   private:
       void DrawCustomFeatures() {
           // Example: Draw player count
           char text[64];
           sprintf_s(text, "Players Online: %d", GetActivePlayerCount());
           DrawText(text, 10, 100, 1.0f, 1.0f);
       }
   };
   ```

2. **Add to CMakeLists.txt**:
   ```cmake
   add_library(MyCustomMod SHARED
       MyCustomMod.cpp
   )
   
   target_link_libraries(MyCustomMod
       BO3ModFramework
       ${DIRECTX_LIBRARIES}
       ${WINDOWS_LIBRARIES}
   )
   ```

### Understanding the Framework

**Core Components**:

1. **BO3_ModFramework.h**: All game structures and function signatures
2. **BO3_ModFramework.cpp**: Implementation and utility functions
3. **ExampleMod.cpp**: Complete example showing all features
4. **Injector.cpp**: DLL injection and hotkey management

**Key Functions**:

```cpp
// Entity Management
CEntity* entity = CG_GetEntity(0, playerIndex);
bool isValid = IsValidEntity(entity);

// Player Information
CClientInfo* client = GetClientByIndex(playerIndex);
bool isEnemy = IsEnemy(client, localTeam);

// Rendering
Vec2 screenPos;
bool visible = WorldToScreen(worldPos, screenPos);
DrawText("Hello World", screenPos.x, screenPos.y, 1.0f, 1.0f);

// Bone System
Vec3 headPos = GetBonePosition(entity, "j_head");
```

## 🎨 Customization Examples

### Custom ESP Colors

```cpp
void DrawCustomESP() {
    // Define custom colors
    float enemyColor[4] = {1.0f, 0.0f, 0.0f, 1.0f};    // Red
    float friendlyColor[4] = {0.0f, 1.0f, 0.0f, 1.0f};  // Green
    float spectatorColor[4] = {1.0f, 1.0f, 0.0f, 1.0f}; // Yellow
    
    for (int i = 1; i < 64; i++) {
        CEntity* entity = CG_GetEntity(0, i);
        if (!IsValidEntity(entity)) continue;
        
        CClientInfo* client = GetClientByIndex(i);
        if (!client) continue;
        
        // Choose color based on team/status
        float* color = friendlyColor;
        if (IsEnemy(client, localTeam)) {
            color = enemyColor;
        } else if (client->iTeam == 0) {
            color = spectatorColor;
        }
        
        DrawPlayerESP(entity, client, color);
    }
}
```

### Custom Information Display

```cpp
void DrawAdvancedInfo() {
    float yPos = 50.0f;
    
    // Game information
    char gameInfo[256];
    sprintf_s(gameInfo, "Map: %s | Mode: %s", GetMapName(), GetGameMode());
    DrawText(gameInfo, 10, yPos, 1.0f, 1.0f);
    yPos += 20;
    
    // Performance info
    sprintf_s(gameInfo, "FPS: %d | Ping: %dms", GetFPS(), GetPing());
    DrawText(gameInfo, 10, yPos, 1.0f, 1.0f);
    yPos += 20;
    
    // Player stats
    sprintf_s(gameInfo, "K/D: %.2f | Score: %d", GetKDRatio(), GetScore());
    DrawText(gameInfo, 10, yPos, 1.0f, 1.0f);
}
```

## 🛠️ Troubleshooting

### Common Issues

**1. "BlackOps3.exe not found"**
- Make sure the game is running
- Verify the process name in Task Manager
- Run injector as Administrator

**2. "Failed to inject DLL"**
- Check if DLL was built successfully
- Verify DLL is 64-bit (same as game)
- Disable antivirus temporarily
- Run as Administrator

**3. "Memory access violations"**
- Wrong game version (not ********)
- Game updated and addresses changed
- Anti-cheat interference

**4. "Functions not working"**
- Verify you're in an active game (not menu)
- Check if game structures have changed
- Enable debug output to trace issues

### Debug Mode

Enable debug output in your mod:

```cpp
#ifdef DEBUG
    char debugText[256];
    sprintf_s(debugText, "Entity Count: %d", entityCount);
    DrawText(debugText, 10, 200, 0.8f, 0.8f);
#endif
```

Build with debug flags:
```bash
cmake --build . --config Debug
```

## 📊 Performance Optimization

### Best Practices

1. **Limit Drawing Operations**:
   ```cpp
   static int frameCounter = 0;
   if (++frameCounter % 5 == 0) {  // Update every 5 frames
       UpdateExpensiveOperations();
   }
   ```

2. **Cache Frequently Used Data**:
   ```cpp
   static CG_T* cachedCGT = nullptr;
   static int lastUpdateFrame = 0;
   
   if (GetCurrentFrame() != lastUpdateFrame) {
       cachedCGT = CG_GetCGT(0);
       lastUpdateFrame = GetCurrentFrame();
   }
   ```

3. **Efficient Entity Iteration**:
   ```cpp
   // Only check active player slots
   for (int i = 1; i < GetMaxPlayers(); i++) {
       if (!IsPlayerConnected(i)) continue;
       // Process player
   }
   ```

## 🔒 Safety Guidelines

### Staying Undetected (Educational Context)

1. **Avoid Obvious Patterns**:
   - Don't make perfect aim movements
   - Add human-like delays and inaccuracies
   - Vary behavior patterns

2. **Limit Functionality**:
   - Don't use all features simultaneously
   - Implement realistic limitations
   - Add randomization to timings

3. **Memory Safety**:
   - Always validate pointers before use
   - Handle exceptions gracefully
   - Clean up resources properly

### Code Example - Safe Entity Access

```cpp
bool SafeEntityAccess(int index, std::function<void(CEntity*)> callback) {
    if (index < 0 || index >= 64) return false;
    
    __try {
        CEntity* entity = CG_GetEntity(0, index);
        if (!entity || !IsValidEntity(entity)) return false;
        
        callback(entity);
        return true;
    }
    __except(EXCEPTION_EXECUTE_HANDLER) {
        // Log error but don't crash
        return false;
    }
}
```

## 📈 Next Steps

1. **Study the Code**: Understand how each component works
2. **Experiment Safely**: Test in offline modes only
3. **Learn Reverse Engineering**: Use tools like Cheat Engine, IDA Pro
4. **Contribute**: Help update addresses for new game versions
5. **Respect Others**: Never use for unfair advantages online

## 🆘 Support

- **Documentation**: Read README.md and TECHNICAL_DETAILS.md
- **Validation**: Run the framework validator regularly
- **Community**: Learn from UnknownCheats and similar educational forums
- **Legal**: Always follow applicable laws and terms of service

Remember: This framework is a learning tool. Use it responsibly and ethically!
