@echo off
echo Building Simple BO3 Mod...
echo.

REM Try to find Visual Studio Developer Command Prompt
set VCVARS=""

REM Check different VS 2022 editions
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    set VCVARS="C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    goto :build
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    set VCVARS="C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
    goto :build
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat" (
    set VCVARS="C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat"
    goto :build
)

echo Visual Studio 2022 not found!
echo Please install Visual Studio 2022 with C++ support.
pause
exit /b 1

:build
echo Found Visual Studio at: %VCVARS%
echo.

REM Initialize Visual Studio environment
call %VCVARS%

echo Building SimpleMod.dll...
cl /LD /EHsc SimpleMod.cpp /Fe:SimpleMod.dll /link kernel32.lib user32.lib

if errorlevel 1 (
    echo.
    echo Failed to build SimpleMod.dll
    pause
    exit /b 1
)

echo Building SimpleInjector.exe...
cl /EHsc SimpleInjector.cpp /Fe:SimpleInjector.exe /link kernel32.lib

if errorlevel 1 (
    echo.
    echo Failed to build SimpleInjector.exe
    pause
    exit /b 1
)

echo.
echo ✅ Build successful!
echo.
echo Files created:
if exist SimpleMod.dll echo ✓ SimpleMod.dll
if exist SimpleInjector.exe echo ✓ SimpleInjector.exe
echo.

echo Ready to inject! Run: SimpleInjector.exe
echo.
echo WARNING: Educational use only!
echo Never use in online multiplayer!
echo.
pause
