@echo off
setlocal enabledelayedexpansion

echo ===============================================
echo Call of Duty: Black Ops 3 Mod Framework Build
echo ===============================================
echo.

:: Check if CMake is available
cmake --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: CMake not found in PATH
    echo Please install CMake and add it to your PATH
    echo Download from: https://cmake.org/download/
    pause
    exit /b 1
)

:: Check if Visual Studio is available
where cl.exe >nul 2>&1
if errorlevel 1 (
    echo ERROR: Visual Studio compiler not found
    echo Please run this script from a Visual Studio Developer Command Prompt
    echo Or install Visual Studio with C++ development tools
    pause
    exit /b 1
)

:: Create build directory
if not exist "build" mkdir build
cd build

echo Setting up build environment...
echo.

:: Configure with CMake
echo Configuring project...
cmake .. -G "Visual Studio 17 2022" -A x64 -DCMAKE_BUILD_TYPE=Release
if errorlevel 1 (
    echo ERROR: CMake configuration failed
    pause
    exit /b 1
)

echo.
echo Building project...

:: Build the project
cmake --build . --config Release --parallel
if errorlevel 1 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo ===============================================
echo Build completed successfully!
echo ===============================================
echo.

:: Check if files were created
if exist "bin\BO3ExampleMod.dll" (
    echo ✓ BO3ExampleMod.dll created
) else (
    echo ✗ BO3ExampleMod.dll not found
)

if exist "bin\BO3Injector.exe" (
    echo ✓ BO3Injector.exe created
) else (
    echo ✗ BO3Injector.exe not found
)

if exist "lib\BO3ModFramework.lib" (
    echo ✓ BO3ModFramework.lib created
) else (
    echo ✗ BO3ModFramework.lib not found
)

echo.
echo Output files location:
echo - DLL: %CD%\bin\BO3ExampleMod.dll
echo - Injector: %CD%\bin\BO3Injector.exe
echo - Library: %CD%\lib\BO3ModFramework.lib
echo.

:: Create package
echo Creating distribution package...
cmake --build . --target package
if exist "package" (
    echo ✓ Distribution package created in: %CD%\package\
) else (
    echo ✗ Package creation failed
)

echo.
echo ===============================================
echo IMPORTANT WARNINGS:
echo ===============================================
echo - This framework is for EDUCATIONAL PURPOSES ONLY
echo - Using mods in online games may result in BANS
echo - Only use in OFFLINE/PRIVATE environments
echo - Game version supported: 37.1.1.0
echo - Requires 64-bit Black Ops 3
echo ===============================================
echo.

echo Usage Instructions:
echo 1. Start Call of Duty: Black Ops 3
echo 2. Run: BO3Injector.exe BO3ExampleMod.dll
echo 3. Use F1-F3 keys to toggle mod features
echo 4. Press ESC in injector to exit
echo.

pause
