#include <Windows.h>
#include <iostream>
#include <thread>
#include <atomic>
#include <fstream>

// Simple mod state
std::atomic<bool> g_espEnabled{false};
std::atomic<bool> g_aimbotEnabled{false};
std::atomic<bool> g_infoEnabled{false};
std::atomic<bool> g_running{true};

// Log file for debugging
std::ofstream g_logFile;

void WriteLog(const std::string& message) {
    if (g_logFile.is_open()) {
        SYSTEMTIME st;
        GetLocalTime(&st);
        g_logFile << "[" << st.wHour << ":" << st.wMinute << ":" << st.wSecond << "] " << message << std::endl;
        g_logFile.flush();
    }
}

// Force console to appear
void ForceConsole() {
    AllocConsole();
    
    // Redirect stdout, stdin, stderr to console
    freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
    freopen_s((FILE**)stderr, "CONOUT$", "w", stderr);
    freopen_s((FILE**)stdin, "CONIN$", "r", stdin);
    
    // Make console window visible and on top
    HWND consoleWindow = GetConsoleWindow();
    if (consoleWindow) {
        SetWindowPos(consoleWindow, HWND_TOPMOST, 100, 100, 800, 600, SWP_SHOWWINDOW);
        SetConsoleTitleA("BO3 Educational Mod - ACTIVE");
        ShowWindow(consoleWindow, SW_SHOW);
        SetForegroundWindow(consoleWindow);
    }
    
    WriteLog("Console allocated and configured");
}

// Show prominent message box
void ShowBigNotification(const char* title, const char* message) {
    // Create a message box that's always on top
    MessageBoxA(nullptr, message, title, MB_OK | MB_ICONINFORMATION | MB_TOPMOST | MB_SETFOREGROUND);
    WriteLog(std::string("Notification shown: ") + title + " - " + message);
}

// Create a visible window
HWND CreateStatusWindow() {
    WNDCLASSA wc = {0};
    wc.lpfnWndProc = DefWindowProcA;
    wc.hInstance = GetModuleHandle(nullptr);
    wc.lpszClassName = "BO3ModStatus";
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
    
    RegisterClassA(&wc);
    
    HWND hwnd = CreateWindowA("BO3ModStatus", "BO3 Mod Status - Educational Demo",
                             WS_OVERLAPPEDWINDOW | WS_VISIBLE,
                             50, 50, 400, 300,
                             nullptr, nullptr, GetModuleHandle(nullptr), nullptr);
    
    if (hwnd) {
        ShowWindow(hwnd, SW_SHOW);
        UpdateWindow(hwnd);
        SetWindowPos(hwnd, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE);
        WriteLog("Status window created");
    }
    
    return hwnd;
}

// Hotkey handler thread
void HotkeyThread() {
    WriteLog("Hotkey thread started");
    
    std::cout << "\n";
    std::cout << "========================================\n";
    std::cout << "BO3 EDUCATIONAL MOD - NOW ACTIVE!\n";
    std::cout << "========================================\n";
    std::cout << "This window proves the mod is loaded!\n";
    std::cout << "\nCONTROLS:\n";
    std::cout << "F1 - Toggle ESP\n";
    std::cout << "F2 - Toggle Aimbot\n";
    std::cout << "F3 - Toggle Info Display\n";
    std::cout << "ESC - Exit mod\n";
    std::cout << "\nWARNING: Educational use only!\n";
    std::cout << "Never use in online multiplayer!\n";
    std::cout << "========================================\n\n";
    
    // Show initial notification
    ShowBigNotification("BO3 MOD LOADED!", 
                       "Educational mod is now active!\n\n"
                       "Look for the console window!\n\n"
                       "Controls:\n"
                       "F1 = ESP\n"
                       "F2 = Aimbot\n"
                       "F3 = Info\n"
                       "ESC = Exit\n\n"
                       "This is for educational purposes only!");
    
    int loopCount = 0;
    while (g_running) {
        // Check for hotkeys
        if (GetAsyncKeyState(VK_F1) & 0x8000) {
            g_espEnabled = !g_espEnabled;
            std::cout << "\n>>> [F1] ESP " << (g_espEnabled ? "ENABLED" : "DISABLED") << " <<<\n" << std::endl;
            ShowBigNotification("ESP TOGGLED", g_espEnabled ? "ESP is now ENABLED" : "ESP is now DISABLED");
            WriteLog(g_espEnabled ? "ESP enabled" : "ESP disabled");
            Sleep(300); // Debounce
        }
        
        if (GetAsyncKeyState(VK_F2) & 0x8000) {
            g_aimbotEnabled = !g_aimbotEnabled;
            std::cout << "\n>>> [F2] AIMBOT " << (g_aimbotEnabled ? "ENABLED" : "DISABLED") << " <<<\n" << std::endl;
            ShowBigNotification("AIMBOT TOGGLED", g_aimbotEnabled ? "Aimbot is now ENABLED" : "Aimbot is now DISABLED");
            WriteLog(g_aimbotEnabled ? "Aimbot enabled" : "Aimbot disabled");
            Sleep(300); // Debounce
        }
        
        if (GetAsyncKeyState(VK_F3) & 0x8000) {
            g_infoEnabled = !g_infoEnabled;
            std::cout << "\n>>> [F3] INFO DISPLAY " << (g_infoEnabled ? "ENABLED" : "DISABLED") << " <<<\n" << std::endl;
            ShowBigNotification("INFO DISPLAY TOGGLED", g_infoEnabled ? "Info Display is now ENABLED" : "Info Display is now DISABLED");
            WriteLog(g_infoEnabled ? "Info display enabled" : "Info display disabled");
            Sleep(300); // Debounce
        }
        
        if (GetAsyncKeyState(VK_ESCAPE) & 0x8000) {
            std::cout << "\n>>> [ESC] EXITING MOD... <<<\n" << std::endl;
            ShowBigNotification("MOD EXITING", "Educational mod is shutting down...");
            WriteLog("Mod exiting via ESC key");
            g_running = false;
            break;
        }
        
        // Show periodic status
        if (loopCount % 200 == 0) { // Every 10 seconds
            std::cout << "\n=== MOD STATUS (Runtime: " << (loopCount * 50 / 1000) << "s) ===\n";
            std::cout << "ESP: " << (g_espEnabled ? "ON" : "OFF") << "\n";
            std::cout << "Aimbot: " << (g_aimbotEnabled ? "ON" : "OFF") << "\n";
            std::cout << "Info: " << (g_infoEnabled ? "ON" : "OFF") << "\n";
            std::cout << "Press F1, F2, F3 to toggle features!\n";
            std::cout << "================================\n\n";
            WriteLog("Status update displayed");
        }
        
        loopCount++;
        Sleep(50);
    }
    
    WriteLog("Hotkey thread exiting");
}

// DLL Entry Point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        // Open log file
        g_logFile.open("BO3_Mod_Log.txt", std::ios::app);
        WriteLog("=== DLL_PROCESS_ATTACH ===");
        
        // Disable thread notifications
        DisableThreadLibraryCalls(hModule);
        
        // Force console to appear
        ForceConsole();
        
        // Create status window
        CreateStatusWindow();
        
        // Start hotkey handler thread
        std::thread(HotkeyThread).detach();
        
        WriteLog("Mod initialization complete");
        break;
        
    case DLL_PROCESS_DETACH:
        WriteLog("=== DLL_PROCESS_DETACH ===");
        g_running = false;
        
        // Show exit notification
        ShowBigNotification("BO3 MOD UNLOADED", "Educational mod has been removed from memory.");
        
        // Close log file
        if (g_logFile.is_open()) {
            WriteLog("Mod unloaded");
            g_logFile.close();
        }
        
        // Free console
        FreeConsole();
        break;
    }
    return TRUE;
}

// Export functions
extern "C" {
    __declspec(dllexport) void ToggleESP() {
        g_espEnabled = !g_espEnabled;
        WriteLog("ESP toggled via API");
    }
    
    __declspec(dllexport) void ToggleAimbot() {
        g_aimbotEnabled = !g_aimbotEnabled;
        WriteLog("Aimbot toggled via API");
    }
    
    __declspec(dllexport) void ToggleInfo() {
        g_infoEnabled = !g_infoEnabled;
        WriteLog("Info toggled via API");
    }
    
    __declspec(dllexport) bool IsESPEnabled() {
        return g_espEnabled;
    }
    
    __declspec(dllexport) bool IsAimbotEnabled() {
        return g_aimbotEnabled;
    }
    
    __declspec(dllexport) bool IsInfoEnabled() {
        return g_infoEnabled;
    }
}
