# Technical Details - Call of Duty: Black Ops 3 Reverse Engineering

This document contains detailed technical information extracted from the UnknownCheats forum thread about Call of Duty: Black Ops 3 reverse engineering.

## 🎯 Game Version Information

**Supported Version**: ********
**Architecture**: x64 (64-bit)
**Engine**: Modified IW Engine (Call of Duty engine)

## 🧠 Memory Layout and Key Addresses

### Core Function Addresses (Version ********)

```cpp
// Entity Management
CG_GetEntity           = 0x140032530
CG_GetCGT              = 0x140032660
CG_GetCGS              = 0x1400858E0
CG_GetPredictedPlayerState = 0x1400F5C90

// Drawing Functions
DrawEngineText         = 0x140405290
GetFont                = 0x1403DCD90
DrawNameTags           = 0x1400CC0F0
DrawStretchPic         = 0x14081D1A0
DrawRotatedStretchPic  = 0x14081D290

// Radar and UI
DrawRadarOverlay       = 0x1405ADAE0
DrawFriendlyIcons      = 0x1405B05F0
DrawEnemyIcons         = 0x1405AEE30

// Shader System
RegisterShader         = 0x140400F20

// World/Screen Conversion
World2Screen           = 0x1400C9C70

// Bone/Tag System
RegisterTag            = 0x14031AC90
GetTagPos              = 0x1400FBDE0

// Weapon System
CG_GetWeaponInfo       = 0x1407E1780

// Spread System
BG_GetSpreadForWeapon  = Pattern: "48 89 5C 24 ? 48 89 54 24 ? 55 56 57 48 83 EC 40 48 8B D9"
```

### Memory Encryption

The game uses encrypted pointers for critical structures. Two decryption functions are provided:

#### CG_T Pointer Decryption
```cpp
static IntPtr DecryptCGPtr(IntPtr ptr) {
    ulong v1 = (ulong)ptr;
    uint v2 = 0xD1CCE915;
    ulong v3 = 0xFFFFFFFF;
    uint v4 = 0x64B92CC4;
    
    // Complex decryption algorithm (see forum thread for full implementation)
    // This changes between game updates
}
```

#### CEntity Pointer Decryption
```cpp
static IntPtr DecryptCEntityPtr(IntPtr ptr) {
    // Similar complex decryption for entity pointers
    // Implementation varies by game version
}
```

## 🏗️ Data Structures Deep Dive

### CEntity Structure Layout
```cpp
class CEntity {
    char unknown0[64];          // 0x0000
    Vec3 vOrigin;              // 0x0040 - World position
    Vec3 vAngles;              // 0x004C - Rotation angles
    char pad1[0x4E8];          // Padding
    BYTE Type;                 // 0x0540 - Entity type (1 = player)
    char pad2[0x3B4];          // Padding
    BYTE IsAlive;              // 0x8F4 - Alive status
    char pad3[0x8];            // Padding
    int flags1;                // 0x0400 - Primary flags
    char pad4[0xD4];           // Padding
    int flags2;                // 0x04D8 - Secondary flags (duplicate)
};
```

### Entity Flags Breakdown
```cpp
enum EFlags {
    EF_STANDING     = 0x00000002,  // Player is standing
    EF_CROUCHING    = 0x00000004,  // Player is crouching
    EF_PRONE        = 0x00000008,  // Player is prone
    EF_FIRING       = 0x00000040,  // Player is firing
    EF_SPRINTING    = 0x00002000,  // Player is sprinting
    EF_DEAD         = 0x00004000,  // Player is dead
    EF_ADS          = 0x00008000   // Player is aiming down sights
};
```

### CClientInfo Structure
```cpp
class CClientInfo {
    char bValidInfo;           // 0x0000 - Valid info flag
    char Unknown001[0x7];      // 0x0001
    int ClientNum;             // 0x0008 - Client number
    char Name[32];             // 0x000C - Player name
    int Team;                  // 0x002C - Team number
    int oldTeam;               // 0x0030 - Previous team
    char pad1[0xB8];           // 0x0034
    int health;                // 0x00EC - Player health
    char pad2[0xDC0];          // 0x00F0
    WORD WpnIndex;             // 0x06F8 - Current weapon index
};
```

### RefDef Structure (Rendering Context)
```cpp
class RefDef {
    DWORD x;                   // 0x0000 - Screen X offset
    DWORD y;                   // 0x0004 - Screen Y offset
    DWORD width;               // 0x0008 - Screen width
    DWORD height;              // 0x000C - Screen height
    char pad_0x0010[0x54];     // 0x0010
    float ZoomProgress;        // 0x0064 - Zoom/scope progress
    char pad_0x0068[0xC];      // 0x0068
    float tanHalfFovX;         // 0x0074 - Horizontal FOV tangent
    float tanHalfFovY;         // 0x0078 - Vertical FOV tangent
    char pad_0x007C[0x8];      // 0x007C
    Vec3 ViewOrigin;           // 0x0084 - Camera world position
    char pad_0x0090[0x10];     // 0x0090
    Vec3 ViewAxis[3];          // 0x00A0 - View matrix (forward, right, up)
    char pad_0x00C4[0x4];      // 0x00C4
    DWORD Time;                // 0x00C8 - Game time
};
```

## 🎮 Game Systems

### Bone/Tag System
The game uses a tag-based bone system for skeletal animation:

```cpp
// Common bone tags
"j_head"           - Head
"j_neck"           - Neck
"j_shoulder_le"    - Left shoulder
"j_shoulder_ri"    - Right shoulder
"j_elbow_le"       - Left elbow
"j_elbow_ri"       - Right elbow
"j_wrist_le"       - Left wrist
"j_wrist_ri"       - Right wrist
"j_spinelower"     - Lower spine
"j_knee_le"        - Left knee
"j_knee_ri"        - Right knee
"j_ankle_le"       - Left ankle
"j_ankle_ri"       - Right ankle
```

### Weapon System
```cpp
class CG_WeaponInfo {
    char* mpName;              // 0x0000 - Multiplayer name
    char* cleanName;           // 0x0008 - Display name
    char pad_0x0010[0x10];     // 0x0010
    char* upperName;           // 0x0020 - Uppercase name
};

// Get weapon info
CG_WeaponInfo* GetWeaponInfo(WORD weaponIndex) {
    return CG_GetWeaponInfo(weaponIndex);
}
```

### Spread System
```cpp
// Weapon spread multiplier location
float spreadMultiplier = *(float*)(CGameBlob + 0x3259A0) / 255.0f;

// BG_GetSpreadForWeapon function can be found with pattern:
// "48 89 5C 24 ? 48 89 54 24 ? 55 56 57 48 83 EC 40 48 8B D9"
```

## 🔄 World to Screen Conversion

The World2Screen function converts 3D world coordinates to 2D screen coordinates:

```cpp
int WorldToScreen(int clientIndex, Vec3& worldPoint, float* screenX, float* screenY) {
    // Get CG_T structure
    CG_T* cgt = CG_GetCGT(clientIndex);
    
    // Get RefDef (rendering context)
    RefDef* refdef = (RefDef*)((DWORD_PTR)cgt + 0x130FB4);
    
    // Perform matrix transformation
    // Implementation involves view matrix multiplication
    // Returns 1 if point is visible, 0 if behind camera
}
```

## 🎨 Drawing System

### Text Rendering
```cpp
void DrawText(char* text, float x, float y, float scaleX, float scaleY) {
    void* font = GetFont();
    float whiteColor[4] = {1.0f, 1.0f, 1.0f, 1.0f};
    
    DrawEngineText(text, 0x7FFFFFFF, font, x, y, scaleX, scaleY, 1.0f, whiteColor, 0);
}
```

### Shader System
```cpp
shader_t* GetShader(char* shaderName) {
    return RegisterShader(shaderName, 7, 1, 0x0FFFFFFFF);
}

// Common shaders
"compassping_enemysatellite_diamond"  - Enemy radar icon
"white"                               - White texture
"black"                               - Black texture
```

## 🔍 Client Information Access

### Getting Client by Index
```cpp
CClientInfo* GetClientByIndex(int index) {
    CG_T* cgt = CG_GetCGT(0);
    if (!cgt) return nullptr;
    
    // Calculate offset: base + client_array_offset + (index * client_size) - adjustment
    return (CClientInfo*)((DWORD_PTR)cgt + 0x2E6BB4 + (index * 0xEB0) - 0x2C);
}
```

### Team Detection
```cpp
bool IsEnemy(CClientInfo* client, int localTeam) {
    return client->Team != localTeam && client->Team != 0;
}
```

## 🎯 Aimbot Considerations

### View Angles
The game stores view angles in multiple locations:
- RefDef contains camera angles for rendering
- ClientActive contains input angles
- UserCmd contains command angles for networking

```cpp
// ClientActive structure (partial)
typedef struct {
    char pad[0x160];
    Vec3 baseAngles;           // 0x0160 - Base view angles
    char pad2[0xB720];
    Vec3 deltaAngles;          // 0xB880 - Delta angles for smooth aiming
} ClientActive;
```

### Angle Calculation
```cpp
Vec3 CalculateAngles(Vec3 source, Vec3 destination) {
    Vec3 delta = destination - source;
    
    float distance = sqrt(delta.x * delta.x + delta.y * delta.y);
    float yaw = atan2(delta.y, delta.x) * (180.0f / M_PI);
    float pitch = -atan2(delta.z, distance) * (180.0f / M_PI);
    
    return Vec3(pitch, yaw, 0);
}
```

## 🛡️ Anti-Cheat Considerations

### Memory Protection
- The game uses encrypted pointers that change between updates
- Critical structures are obfuscated
- Anti-debugging measures are present

### Detection Vectors
- Memory pattern scanning
- API hook detection
- Behavioral analysis
- Statistical analysis of player performance

### Mitigation Strategies
- Use external memory reading when possible
- Implement human-like behavior patterns
- Avoid obvious statistical anomalies
- Use legitimate game functions when available

## 🔧 Debugging and Development

### Recommended Tools
- **Cheat Engine**: Memory scanning and debugging
- **x64dbg**: Dynamic analysis and debugging
- **IDA Pro/Ghidra**: Static analysis and reverse engineering
- **Process Hacker**: Process and memory analysis

### Development Tips
1. Always verify game version before using addresses
2. Use pattern scanning for version-independent code
3. Implement proper error handling for all memory operations
4. Test thoroughly in offline environments
5. Keep backups of working configurations

## 📊 Performance Considerations

### Optimization Tips
- Cache frequently accessed pointers
- Minimize memory allocations in hot paths
- Use efficient data structures for entity lists
- Implement proper frame rate limiting for drawing operations

### Memory Usage
- Be mindful of memory leaks in long-running mods
- Use smart pointers or RAII patterns
- Clean up resources properly on mod unload

## 🔄 Version Updates

When the game updates, the following typically change:
- Memory addresses for functions
- Structure layouts and offsets
- Encryption algorithms for pointers
- Anti-cheat signatures and patterns

### Update Process
1. Identify new game version
2. Use pattern scanning to find updated addresses
3. Verify structure layouts haven't changed
4. Test all functionality thoroughly
5. Update documentation and version info

This technical information forms the foundation for creating robust and maintainable mods for Call of Duty: Black Ops 3.
