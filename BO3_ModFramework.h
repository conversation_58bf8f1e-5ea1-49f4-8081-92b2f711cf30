#pragma once
#include <Windows.h>
#include <d3d9.h>
#include <d3dx9.h>

// Game version: ******** (from the forum thread)

// ========== BASIC TYPES ==========
typedef struct {
    float x, y, z;
} Vec3;

typedef struct {
    float x, y;
} Vec2;

typedef void* shader_t;

// ========== GAME STRUCTURES ==========

// Entity structure
class CEntity {
public:
    char unknown0[64];
    Vec3 vOrigin;           // 0x40
    Vec3 vAngles;           // 0x4C
    char pad1[0x4E8];       // Padding to reach flags
    BYTE Type;              // 0x540 - Entity type
    char pad2[0x3B4];       // Padding
    BYTE IsAlive;           // 0x8F4 - Alive status
    char pad3[0x8];         // Padding
    int flags1;             // 0x400 - First set of flags
    char pad4[0xD4];        // Padding
    int flags2;             // 0x4D8 - Second set of flags
};

// Client info structure
class CClientInfo {
public:
    char unknown0[12];
    char szName[16];        // 0x000C - Player name
    char unknown1[16];
    int iTeam;              // 0x002C - Team number
    char pad1[0x6CC];       // Padding
    WORD WpnIndex;          // 0x06F8 - Weapon index
};

// CG_T structure
class CG_T {
public:
    int LocalIndex;         // 0x0000 - Local client index
    char pad[0x130FB0];     // Padding to refdef
    // RefDef starts at offset 0x130FB4
};

// RefDef structure for rendering
class RefDef {
public:
    DWORD x;                // 0x0000 - Screen X
    DWORD y;                // 0x0004 - Screen Y  
    DWORD width;            // 0x0008 - Screen width
    DWORD height;           // 0x000C - Screen height
    char pad_0x0010[0x54];  // 0x0010
    float ZoomProgress;     // 0x0064
    char pad_0x0068[0xC];   // 0x0068
    float tanHalfFovX;      // 0x0074
    float tanHalfFovY;      // 0x0078
    char pad_0x007C[0x8];   // 0x007C
    Vec3 ViewOrigin;        // 0x0084 - Camera position
    char pad_0x0090[0x10];  // 0x0090
    Vec3 ViewAxis[3];       // 0x00A0 - View matrix
    char pad_0x00C4[0x4];   // 0x00C4
    DWORD Time;             // 0x00C8 - Game time
};

// Player state structure
typedef struct {
    char _0x0000[0x20];
    int otherFlags;         // 0x0020 - Status flags (IsInGame, etc.)
} playerState_s;

// CGS structure for game state
typedef struct {
    char _0x0000[48];
    char gametype[32];      // 0x0030 - Game type
    char hostname[256];     // 0x0050 - Server hostname
    char _0x0150[8];
    char mapPath[64];       // 0x0158 - Map file path
    char mapName[32];       // 0x0198 - Map display name
    char _0x01B8[0x1E6C8];
} cgs_t;

// Weapon info structure
class CG_WeaponInfo {
public:
    char* mpName;           // 0x0000 - Weapon MP name
    char* cleanName;        // 0x0008 - Clean display name
    char pad_0x0010[0x10];  // 0x0010
    char* upperName;        // 0x0020 - Uppercase name
};

// ========== ENTITY FLAGS ==========
enum EFlags {
    EF_STANDING     = 0x00000002,
    EF_CROUCHING    = 0x00000004,
    EF_PRONE        = 0x00000008,
    EF_FIRING       = 0x00000040,
    EF_SPRINTING    = 0x00002000,
    EF_DEAD         = 0x00004000,
    EF_ADS          = 0x00008000
};

// ========== FUNCTION SIGNATURES ==========

// Core game functions
typedef CEntity* (*CG_GetEntity_)(int a1, int idx);
typedef CG_T* (*CG_GetCGT_)(int a1);
typedef cgs_t* (*CG_GetCGS_)(int idx);
typedef playerState_s* (*CG_GetPredictedPlayerState_)(int cgIndex);

// Drawing functions
typedef int (*DrawEngineText_)(char* Text, int NumCharsToRender, void* Font, 
                              float X, float Y, float W, float H, float stuff, 
                              float* Color, int unknown);
typedef void* (*GetFont_)();
typedef void (*DrawNameTags_)(int style, CEntity* pEnt, float fAlpha, int entityId);

// Shader and rendering
typedef shader_t* (*RegisterShader_)(char* szShader, int a2, int a3, int a4);
typedef int (*DrawStretchPic_)(float x, float y, float w, float h, 
                              float s0, float t0, float s1, float t1, 
                              float* color, shader_t* shader);

// World to screen conversion
typedef int (*World2Screen_)(int idx, Vec3& vPoint, float* x, float* y);

// Tag and bone functions
typedef WORD (*RegisterTag_)(char* szBone);
typedef int (*GetTagPos_)(CEntity* pEnt, WORD boneIdx, Vec3* vOut);

// Weapon functions
typedef CG_WeaponInfo* (*CG_GetWeaponInfo_)(WORD wpnIdx);

// ========== MEMORY ADDRESSES (Version ********) ==========
extern CG_GetEntity_ CG_GetEntity;
extern CG_GetCGT_ CG_GetCGT;
extern CG_GetCGS_ CG_GetCGS;
extern CG_GetPredictedPlayerState_ CG_GetPredictedPlayerState;
extern DrawEngineText_ DrawEngineText;
extern GetFont_ GetFont;
extern DrawNameTags_ DrawNameTags;
extern RegisterShader_ RegisterShader;
extern World2Screen_ World2Screen;
extern RegisterTag_ RegisterTag;
extern GetTagPos_ GetTagPos;
extern CG_GetWeaponInfo_ CG_GetWeaponInfo;

// ========== UTILITY FUNCTIONS ==========

// Get client info by index
CClientInfo* GetClientByIndex(int idx);

// Draw text helper
void DrawText(char* szText, float x, float y, float scalex, float scaley);

// Get shader helper
shader_t* GetShader(char* szShader);

// World to screen helper
bool WorldToScreen(Vec3 worldPos, Vec2& screenPos);

// Bone position helper
Vec3 GetBonePosition(CEntity* entity, const char* boneName);

// Team check helper
bool IsEnemy(CClientInfo* client, int localTeam);

// Entity validation
bool IsValidEntity(CEntity* entity);

// Distance calculation
float GetDistance(Vec3 pos1, Vec3 pos2);

#endif // BO3_MODFRAMEWORK_H
