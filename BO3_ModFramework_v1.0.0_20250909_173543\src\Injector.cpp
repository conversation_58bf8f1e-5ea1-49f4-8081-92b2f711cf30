#include <Windows.h>
#include <iostream>
#include <string>
#include <TlHelp32.h>

// Simple DLL injector for the BO3 mod framework

class DLLInjector {
private:
    DWORD GetProcessId(const std::wstring& processName) {
        DWORD processId = 0;
        HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        
        if (snapshot != INVALID_HANDLE_VALUE) {
            PROCESSENTRY32W processEntry;
            processEntry.dwSize = sizeof(processEntry);
            
            if (Process32FirstW(snapshot, &processEntry)) {
                do {
                    if (processName == processEntry.szExeFile) {
                        processId = processEntry.th32ProcessID;
                        break;
                    }
                } while (Process32NextW(snapshot, &processEntry));
            }
            CloseHandle(snapshot);
        }
        
        return processId;
    }
    
    bool InjectDLL(DWORD processId, const std::string& dllPath) {
        HANDLE process = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
        if (!process) {
            std::cout << "Failed to open process. Error: " << GetLastError() << std::endl;
            return false;
        }
        
        // Allocate memory in target process
        LPVOID allocatedMemory = VirtualAllocEx(process, nullptr, dllPath.length() + 1, 
                                               MEM_RESERVE | MEM_COMMIT, PAGE_READWRITE);
        if (!allocatedMemory) {
            std::cout << "Failed to allocate memory. Error: " << GetLastError() << std::endl;
            CloseHandle(process);
            return false;
        }
        
        // Write DLL path to allocated memory
        if (!WriteProcessMemory(process, allocatedMemory, dllPath.c_str(), 
                               dllPath.length() + 1, nullptr)) {
            std::cout << "Failed to write memory. Error: " << GetLastError() << std::endl;
            VirtualFreeEx(process, allocatedMemory, 0, MEM_RELEASE);
            CloseHandle(process);
            return false;
        }
        
        // Get LoadLibraryA address
        HMODULE kernel32 = GetModuleHandleA("kernel32.dll");
        FARPROC loadLibraryAddr = GetProcAddress(kernel32, "LoadLibraryA");
        
        if (!loadLibraryAddr) {
            std::cout << "Failed to get LoadLibraryA address." << std::endl;
            VirtualFreeEx(process, allocatedMemory, 0, MEM_RELEASE);
            CloseHandle(process);
            return false;
        }
        
        // Create remote thread to load DLL
        HANDLE remoteThread = CreateRemoteThread(process, nullptr, 0, 
                                                (LPTHREAD_START_ROUTINE)loadLibraryAddr,
                                                allocatedMemory, 0, nullptr);
        if (!remoteThread) {
            std::cout << "Failed to create remote thread. Error: " << GetLastError() << std::endl;
            VirtualFreeEx(process, allocatedMemory, 0, MEM_RELEASE);
            CloseHandle(process);
            return false;
        }
        
        // Wait for thread to complete
        WaitForSingleObject(remoteThread, INFINITE);
        
        // Cleanup
        CloseHandle(remoteThread);
        VirtualFreeEx(process, allocatedMemory, 0, MEM_RELEASE);
        CloseHandle(process);
        
        return true;
    }
    
public:
    bool InjectIntoBO3(const std::string& dllPath) {
        std::cout << "Looking for BlackOps3.exe process..." << std::endl;
        
        DWORD processId = GetProcessId(L"BlackOps3.exe");
        if (processId == 0) {
            std::cout << "BlackOps3.exe not found. Make sure the game is running." << std::endl;
            return false;
        }
        
        std::cout << "Found BlackOps3.exe (PID: " << processId << ")" << std::endl;
        std::cout << "Injecting DLL: " << dllPath << std::endl;
        
        if (InjectDLL(processId, dllPath)) {
            std::cout << "DLL injected successfully!" << std::endl;
            return true;
        } else {
            std::cout << "Failed to inject DLL." << std::endl;
            return false;
        }
    }
};

// Hotkey handler for mod controls
class HotkeyManager {
private:
    HMODULE modDLL;
    
    // Function pointers to mod controls
    typedef void (*ToggleESPFunc)();
    typedef void (*ToggleAimbotFunc)();
    typedef void (*ToggleInfoDisplayFunc)();
    
    ToggleESPFunc ToggleESP;
    ToggleAimbotFunc ToggleAimbot;
    ToggleInfoDisplayFunc ToggleInfoDisplay;
    
public:
    HotkeyManager() : modDLL(nullptr), ToggleESP(nullptr), 
                      ToggleAimbot(nullptr), ToggleInfoDisplay(nullptr) {}
    
    bool Initialize(const std::string& dllPath) {
        modDLL = LoadLibraryA(dllPath.c_str());
        if (!modDLL) {
            std::cout << "Failed to load mod DLL for hotkey management." << std::endl;
            return false;
        }
        
        // Get function addresses
        ToggleESP = (ToggleESPFunc)GetProcAddress(modDLL, "ToggleESP");
        ToggleAimbot = (ToggleAimbotFunc)GetProcAddress(modDLL, "ToggleAimbot");
        ToggleInfoDisplay = (ToggleInfoDisplayFunc)GetProcAddress(modDLL, "ToggleInfoDisplay");
        
        if (!ToggleESP || !ToggleAimbot || !ToggleInfoDisplay) {
            std::cout << "Failed to get mod function addresses." << std::endl;
            return false;
        }
        
        return true;
    }
    
    void ProcessHotkeys() {
        // F1 - Toggle ESP
        if (GetAsyncKeyState(VK_F1) & 0x8000) {
            if (ToggleESP) {
                ToggleESP();
                std::cout << "ESP toggled" << std::endl;
            }
            Sleep(200); // Prevent rapid toggling
        }
        
        // F2 - Toggle Aimbot
        if (GetAsyncKeyState(VK_F2) & 0x8000) {
            if (ToggleAimbot) {
                ToggleAimbot();
                std::cout << "Aimbot toggled" << std::endl;
            }
            Sleep(200);
        }
        
        // F3 - Toggle Info Display
        if (GetAsyncKeyState(VK_F3) & 0x8000) {
            if (ToggleInfoDisplay) {
                ToggleInfoDisplay();
                std::cout << "Info display toggled" << std::endl;
            }
            Sleep(200);
        }
        
        // ESC - Exit
        if (GetAsyncKeyState(VK_ESCAPE) & 0x8000) {
            std::cout << "Exiting..." << std::endl;
            exit(0);
        }
    }
    
    ~HotkeyManager() {
        if (modDLL) {
            FreeLibrary(modDLL);
        }
    }
};

void PrintUsage() {
    std::cout << "=== Call of Duty: Black Ops 3 Mod Injector ===" << std::endl;
    std::cout << "Usage: Injector.exe <path_to_mod_dll>" << std::endl;
    std::cout << std::endl;
    std::cout << "Controls (after injection):" << std::endl;
    std::cout << "F1 - Toggle ESP" << std::endl;
    std::cout << "F2 - Toggle Aimbot" << std::endl;
    std::cout << "F3 - Toggle Info Display" << std::endl;
    std::cout << "ESC - Exit injector" << std::endl;
    std::cout << std::endl;
    std::cout << "WARNING: Use only in offline/private environments!" << std::endl;
    std::cout << "=========================================" << std::endl;
}

int main(int argc, char* argv[]) {
    PrintUsage();
    
    std::string dllPath;
    
    if (argc < 2) {
        std::cout << "Enter path to mod DLL: ";
        std::getline(std::cin, dllPath);
    } else {
        dllPath = argv[1];
    }
    
    // Check if DLL file exists
    DWORD fileAttributes = GetFileAttributesA(dllPath.c_str());
    if (fileAttributes == INVALID_FILE_ATTRIBUTES) {
        std::cout << "DLL file not found: " << dllPath << std::endl;
        std::cout << "Press any key to exit..." << std::endl;
        std::cin.get();
        return 1;
    }
    
    // Convert to full path
    char fullPath[MAX_PATH];
    GetFullPathNameA(dllPath.c_str(), MAX_PATH, fullPath, nullptr);
    dllPath = fullPath;
    
    DLLInjector injector;
    
    // Wait for user confirmation
    std::cout << "Make sure Black Ops 3 is running, then press Enter to inject...";
    std::cin.get();
    
    if (!injector.InjectIntoBO3(dllPath)) {
        std::cout << "Injection failed. Press any key to exit..." << std::endl;
        std::cin.get();
        return 1;
    }
    
    // Initialize hotkey management
    HotkeyManager hotkeyManager;
    if (hotkeyManager.Initialize(dllPath)) {
        std::cout << "Hotkey management initialized. Use F1-F3 to control mod features." << std::endl;
        std::cout << "Press ESC to exit." << std::endl;
        
        // Hotkey loop
        while (true) {
            hotkeyManager.ProcessHotkeys();
            Sleep(50); // Small delay to prevent high CPU usage
        }
    } else {
        std::cout << "Hotkey management failed to initialize." << std::endl;
        std::cout << "Mod injected but no hotkey controls available." << std::endl;
        std::cout << "Press any key to exit..." << std::endl;
        std::cin.get();
    }
    
    return 0;
}
