#include <Windows.h>
#include <CommCtrl.h>
#include <string>
#include <vector>
#include "resource.h"

#pragma comment(lib, "comctl32.lib")

// ========== BO3 MOD GUI APPLICATION ==========

class BO3ModGUI {
private:
    HWND hMainWindow;
    HW<PERSON> hStatusBar;
    HMODULE hModDLL;
    bool isInjected;
    
    // Control handles
    HWND hInjectBtn, hEjectBtn;
    HWND hESPCheck, hAimbotCheck, hInfoCheck;
    HWND hAdvancedESPCheck, hRadarCheck, hTriggerCheck;
    HWND hProcessList, hDLLPath;
    HWND hLogOutput;
    
    // Function pointers to mod controls
    typedef void (*ToggleFunction)();
    ToggleFunction ToggleESP;
    ToggleFunction ToggleAimbot;
    ToggleFunction ToggleInfoDisplay;
    ToggleFunction TogglePredictiveESP;
    ToggleFunction ToggleRadarHack;
    ToggleFunction ToggleTriggerBot;
    
public:
    BO3ModGUI() : hMainWindow(nullptr), hModDLL(nullptr), isInjected(false),
                  ToggleESP(nullptr), ToggleAimbot(nullptr), ToggleInfoDisplay(nullptr),
                  TogglePredictiveESP(nullptr), ToggleRadarHack(nullptr), ToggleTriggerBot(nullptr) {}
    
    bool Initialize(HINSTANCE hInstance) {
        // Register window class
        WNDCLASSEX wc = {0};
        wc.cbSize = sizeof(WNDCLASSEX);
        wc.style = CS_HREDRAW | CS_VREDRAW;
        wc.lpfnWndProc = WindowProc;
        wc.hInstance = hInstance;
        wc.hIcon = LoadIcon(nullptr, IDI_APPLICATION);
        wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.lpszClassName = L"BO3ModGUI";
        wc.hIconSm = LoadIcon(nullptr, IDI_APPLICATION);
        
        if (!RegisterClassEx(&wc)) {
            return false;
        }
        
        // Create main window
        hMainWindow = CreateWindowEx(
            WS_EX_CLIENTEDGE,
            L"BO3ModGUI",
            L"Call of Duty: Black Ops 3 - Mod Control Panel",
            WS_OVERLAPPEDWINDOW,
            CW_USEDEFAULT, CW_USEDEFAULT, 800, 600,
            nullptr, nullptr, hInstance, this
        );
        
        if (!hMainWindow) {
            return false;
        }
        
        CreateControls();
        UpdateProcessList();
        
        ShowWindow(hMainWindow, SW_SHOW);
        UpdateWindow(hMainWindow);
        
        return true;
    }
    
    void CreateControls() {
        HINSTANCE hInst = GetModuleHandle(nullptr);
        
        // Create status bar
        hStatusBar = CreateWindowEx(0, STATUSCLASSNAME, nullptr,
            WS_CHILD | WS_VISIBLE | SBARS_SIZEGRIP,
            0, 0, 0, 0, hMainWindow, nullptr, hInst, nullptr);
        
        // Injection section
        CreateWindow(L"STATIC", L"Target Process:",
            WS_VISIBLE | WS_CHILD,
            20, 20, 100, 20, hMainWindow, nullptr, hInst, nullptr);
        
        hProcessList = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST | WS_VSCROLL,
            130, 18, 200, 200, hMainWindow, (HMENU)ID_PROCESS_LIST, hInst, nullptr);
        
        CreateWindow(L"BUTTON", L"Refresh",
            WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            340, 18, 80, 25, hMainWindow, (HMENU)ID_REFRESH_PROCESSES, hInst, nullptr);
        
        CreateWindow(L"STATIC", L"DLL Path:",
            WS_VISIBLE | WS_CHILD,
            20, 55, 100, 20, hMainWindow, nullptr, hInst, nullptr);
        
        hDLLPath = CreateWindow(L"EDIT", L"BO3ExampleMod.dll",
            WS_VISIBLE | WS_CHILD | WS_BORDER | ES_AUTOHSCROLL,
            130, 53, 200, 25, hMainWindow, nullptr, hInst, nullptr);
        
        CreateWindow(L"BUTTON", L"Browse",
            WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            340, 53, 80, 25, hMainWindow, (HMENU)ID_BROWSE_DLL, hInst, nullptr);
        
        hInjectBtn = CreateWindow(L"BUTTON", L"Inject DLL",
            WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            450, 18, 100, 30, hMainWindow, (HMENU)ID_INJECT, hInst, nullptr);
        
        hEjectBtn = CreateWindow(L"BUTTON", L"Eject DLL",
            WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON | WS_DISABLED,
            450, 53, 100, 30, hMainWindow, (HMENU)ID_EJECT, hInst, nullptr);
        
        // Feature controls section
        CreateWindow(L"STATIC", L"Basic Features:",
            WS_VISIBLE | WS_CHILD | SS_LEFT,
            20, 100, 200, 20, hMainWindow, nullptr, hInst, nullptr);
        
        hESPCheck = CreateWindow(L"BUTTON", L"ESP (Player Highlighting)",
            WS_VISIBLE | WS_CHILD | BS_AUTOCHECKBOX,
            30, 125, 200, 20, hMainWindow, (HMENU)ID_ESP_CHECK, hInst, nullptr);
        
        hAimbotCheck = CreateWindow(L"BUTTON", L"Aimbot Assistance",
            WS_VISIBLE | WS_CHILD | BS_AUTOCHECKBOX,
            30, 150, 200, 20, hMainWindow, (HMENU)ID_AIMBOT_CHECK, hInst, nullptr);
        
        hInfoCheck = CreateWindow(L"BUTTON", L"Information Display",
            WS_VISIBLE | WS_CHILD | BS_AUTOCHECKBOX,
            30, 175, 200, 20, hMainWindow, (HMENU)ID_INFO_CHECK, hInst, nullptr);
        
        CreateWindow(L"STATIC", L"Advanced Features:",
            WS_VISIBLE | WS_CHILD | SS_LEFT,
            300, 100, 200, 20, hMainWindow, nullptr, hInst, nullptr);
        
        hAdvancedESPCheck = CreateWindow(L"BUTTON", L"Predictive ESP",
            WS_VISIBLE | WS_CHILD | BS_AUTOCHECKBOX,
            310, 125, 200, 20, hMainWindow, (HMENU)ID_ADVANCED_ESP_CHECK, hInst, nullptr);
        
        hRadarCheck = CreateWindow(L"BUTTON", L"Radar Hack",
            WS_VISIBLE | WS_CHILD | BS_AUTOCHECKBOX,
            310, 150, 200, 20, hMainWindow, (HMENU)ID_RADAR_CHECK, hInst, nullptr);
        
        hTriggerCheck = CreateWindow(L"BUTTON", L"Trigger Bot",
            WS_VISIBLE | WS_CHILD | BS_AUTOCHECKBOX,
            310, 175, 200, 20, hMainWindow, (HMENU)ID_TRIGGER_CHECK, hInst, nullptr);
        
        // Warning section
        CreateWindow(L"STATIC", L"⚠️ WARNING: Educational use only! Never use online!",
            WS_VISIBLE | WS_CHILD | SS_CENTER,
            20, 210, 520, 20, hMainWindow, nullptr, hInst, nullptr);
        
        // Log output
        CreateWindow(L"STATIC", L"Activity Log:",
            WS_VISIBLE | WS_CHILD,
            20, 240, 100, 20, hMainWindow, nullptr, hInst, nullptr);
        
        hLogOutput = CreateWindow(L"EDIT", nullptr,
            WS_VISIBLE | WS_CHILD | WS_BORDER | WS_VSCROLL | ES_MULTILINE | ES_READONLY,
            20, 265, 520, 200, hMainWindow, nullptr, hInst, nullptr);
        
        // Hotkey instructions
        CreateWindow(L"STATIC", L"Hotkeys: F1=ESP, F2=Aimbot, F3=Info, F4=Advanced ESP, F5=Radar, F6=Trigger",
            WS_VISIBLE | WS_CHILD | SS_CENTER,
            20, 480, 520, 20, hMainWindow, nullptr, hInst, nullptr);
        
        LogMessage(L"BO3 Mod Control Panel initialized.");
        LogMessage(L"Select BlackOps3.exe process and inject DLL to begin.");
    }
    
    void UpdateProcessList() {
        SendMessage(hProcessList, CB_RESETCONTENT, 0, 0);
        
        HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (snapshot != INVALID_HANDLE_VALUE) {
            PROCESSENTRY32W pe;
            pe.dwSize = sizeof(pe);
            
            if (Process32FirstW(snapshot, &pe)) {
                do {
                    if (wcsstr(pe.szExeFile, L"BlackOps3.exe") || 
                        wcsstr(pe.szExeFile, L"notepad.exe")) { // Include notepad for testing
                        
                        wchar_t processInfo[256];
                        swprintf_s(processInfo, L"%s (PID: %d)", pe.szExeFile, pe.th32ProcessID);
                        
                        int index = SendMessage(hProcessList, CB_ADDSTRING, 0, (LPARAM)processInfo);
                        SendMessage(hProcessList, CB_SETITEMDATA, index, pe.th32ProcessID);
                    }
                } while (Process32NextW(snapshot, &pe));
            }
            CloseHandle(snapshot);
        }
        
        if (SendMessage(hProcessList, CB_GETCOUNT, 0, 0) > 0) {
            SendMessage(hProcessList, CB_SETCURSEL, 0, 0);
        }
    }
    
    void LogMessage(const wchar_t* message) {
        if (!hLogOutput) return;
        
        // Get current time
        SYSTEMTIME st;
        GetLocalTime(&st);
        
        wchar_t timeStr[64];
        swprintf_s(timeStr, L"[%02d:%02d:%02d] ", st.wHour, st.wMinute, st.wSecond);
        
        // Append to log
        int len = GetWindowTextLength(hLogOutput);
        SendMessage(hLogOutput, EM_SETSEL, len, len);
        SendMessage(hLogOutput, EM_REPLACESEL, FALSE, (LPARAM)timeStr);
        SendMessage(hLogOutput, EM_REPLACESEL, FALSE, (LPARAM)message);
        SendMessage(hLogOutput, EM_REPLACESEL, FALSE, (LPARAM)L"\r\n");
        
        // Scroll to bottom
        SendMessage(hLogOutput, EM_SCROLLCARET, 0, 0);
    }
    
    void InjectDLL() {
        int selIndex = SendMessage(hProcessList, CB_GETCURSEL, 0, 0);
        if (selIndex == CB_ERR) {
            LogMessage(L"❌ No process selected!");
            return;
        }
        
        DWORD processId = SendMessage(hProcessList, CB_GETITEMDATA, selIndex, 0);
        
        wchar_t dllPath[MAX_PATH];
        GetWindowText(hDLLPath, dllPath, MAX_PATH);
        
        if (wcslen(dllPath) == 0) {
            LogMessage(L"❌ No DLL path specified!");
            return;
        }
        
        // Convert to full path
        wchar_t fullPath[MAX_PATH];
        GetFullPathName(dllPath, MAX_PATH, fullPath, nullptr);
        
        // Check if file exists
        if (GetFileAttributes(fullPath) == INVALID_FILE_ATTRIBUTES) {
            LogMessage(L"❌ DLL file not found!");
            return;
        }
        
        LogMessage(L"🔄 Attempting DLL injection...");
        
        if (PerformInjection(processId, fullPath)) {
            LogMessage(L"✅ DLL injected successfully!");
            isInjected = true;
            EnableWindow(hInjectBtn, FALSE);
            EnableWindow(hEjectBtn, TRUE);
            
            // Load function pointers
            LoadModuleFunctions();
            
            // Update status
            SetWindowText(hStatusBar, L"Status: Injected and ready");
        } else {
            LogMessage(L"❌ DLL injection failed!");
        }
    }
    
    bool PerformInjection(DWORD processId, const wchar_t* dllPath) {
        HANDLE process = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
        if (!process) return false;
        
        // Convert to ANSI for injection
        char ansiPath[MAX_PATH];
        WideCharToMultiByte(CP_ACP, 0, dllPath, -1, ansiPath, MAX_PATH, nullptr, nullptr);
        
        LPVOID allocatedMemory = VirtualAllocEx(process, nullptr, strlen(ansiPath) + 1,
                                               MEM_RESERVE | MEM_COMMIT, PAGE_READWRITE);
        if (!allocatedMemory) {
            CloseHandle(process);
            return false;
        }
        
        if (!WriteProcessMemory(process, allocatedMemory, ansiPath, strlen(ansiPath) + 1, nullptr)) {
            VirtualFreeEx(process, allocatedMemory, 0, MEM_RELEASE);
            CloseHandle(process);
            return false;
        }
        
        HMODULE kernel32 = GetModuleHandleA("kernel32.dll");
        FARPROC loadLibraryAddr = GetProcAddress(kernel32, "LoadLibraryA");
        
        HANDLE remoteThread = CreateRemoteThread(process, nullptr, 0,
                                                (LPTHREAD_START_ROUTINE)loadLibraryAddr,
                                                allocatedMemory, 0, nullptr);
        if (!remoteThread) {
            VirtualFreeEx(process, allocatedMemory, 0, MEM_RELEASE);
            CloseHandle(process);
            return false;
        }
        
        WaitForSingleObject(remoteThread, INFINITE);
        
        CloseHandle(remoteThread);
        VirtualFreeEx(process, allocatedMemory, 0, MEM_RELEASE);
        CloseHandle(process);
        
        return true;
    }
    
    void LoadModuleFunctions() {
        // Try to load the injected DLL locally to get function pointers
        hModDLL = LoadLibraryA("BO3ExampleMod.dll");
        if (hModDLL) {
            ToggleESP = (ToggleFunction)GetProcAddress(hModDLL, "ToggleESP");
            ToggleAimbot = (ToggleFunction)GetProcAddress(hModDLL, "ToggleAimbot");
            ToggleInfoDisplay = (ToggleFunction)GetProcAddress(hModDLL, "ToggleInfoDisplay");
            TogglePredictiveESP = (ToggleFunction)GetProcAddress(hModDLL, "TogglePredictiveESP");
            ToggleRadarHack = (ToggleFunction)GetProcAddress(hModDLL, "ToggleRadarHack");
            ToggleTriggerBot = (ToggleFunction)GetProcAddress(hModDLL, "ToggleTriggerBot");
            
            LogMessage(L"📡 Function pointers loaded successfully!");
        } else {
            LogMessage(L"⚠️ Could not load function pointers (hotkeys still work)");
        }
    }
    
    void HandleCommand(WPARAM wParam) {
        switch (LOWORD(wParam)) {
        case ID_REFRESH_PROCESSES:
            UpdateProcessList();
            LogMessage(L"🔄 Process list refreshed");
            break;
            
        case ID_BROWSE_DLL:
            BrowseForDLL();
            break;
            
        case ID_INJECT:
            InjectDLL();
            break;
            
        case ID_EJECT:
            EjectDLL();
            break;
            
        case ID_ESP_CHECK:
            if (ToggleESP) {
                ToggleESP();
                LogMessage(L"🎯 ESP toggled");
            } else {
                LogMessage(L"⚠️ Use F1 hotkey (function not available)");
            }
            break;
            
        case ID_AIMBOT_CHECK:
            if (ToggleAimbot) {
                ToggleAimbot();
                LogMessage(L"🎯 Aimbot toggled");
            } else {
                LogMessage(L"⚠️ Use F2 hotkey (function not available)");
            }
            break;
            
        case ID_INFO_CHECK:
            if (ToggleInfoDisplay) {
                ToggleInfoDisplay();
                LogMessage(L"📊 Info display toggled");
            } else {
                LogMessage(L"⚠️ Use F3 hotkey (function not available)");
            }
            break;
            
        case ID_ADVANCED_ESP_CHECK:
            if (TogglePredictiveESP) {
                TogglePredictiveESP();
                LogMessage(L"🔮 Predictive ESP toggled");
            } else {
                LogMessage(L"⚠️ Use F4 hotkey (function not available)");
            }
            break;
            
        case ID_RADAR_CHECK:
            if (ToggleRadarHack) {
                ToggleRadarHack();
                LogMessage(L"📡 Radar hack toggled");
            } else {
                LogMessage(L"⚠️ Use F5 hotkey (function not available)");
            }
            break;
            
        case ID_TRIGGER_CHECK:
            if (ToggleTriggerBot) {
                ToggleTriggerBot();
                LogMessage(L"⚡ Trigger bot toggled");
            } else {
                LogMessage(L"⚠️ Use F6 hotkey (function not available)");
            }
            break;
        }
    }
    
    void BrowseForDLL() {
        OPENFILENAME ofn = {0};
        wchar_t fileName[MAX_PATH] = {0};
        
        ofn.lStructSize = sizeof(ofn);
        ofn.hwndOwner = hMainWindow;
        ofn.lpstrFile = fileName;
        ofn.nMaxFile = MAX_PATH;
        ofn.lpstrFilter = L"DLL Files\0*.dll\0All Files\0*.*\0";
        ofn.nFilterIndex = 1;
        ofn.Flags = OFN_PATHMUSTEXIST | OFN_FILEMUSTEXIST;
        
        if (GetOpenFileName(&ofn)) {
            SetWindowText(hDLLPath, fileName);
            LogMessage(L"📁 DLL path selected");
        }
    }
    
    void EjectDLL() {
        if (hModDLL) {
            FreeLibrary(hModDLL);
            hModDLL = nullptr;
        }
        
        isInjected = false;
        EnableWindow(hInjectBtn, TRUE);
        EnableWindow(hEjectBtn, FALSE);
        
        LogMessage(L"⏏️ DLL ejected");
        SetWindowText(hStatusBar, L"Status: Ready");
    }
    
    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
        BO3ModGUI* gui = nullptr;
        
        if (uMsg == WM_NCCREATE) {
            CREATESTRUCT* cs = (CREATESTRUCT*)lParam;
            gui = (BO3ModGUI*)cs->lpCreateParams;
            SetWindowLongPtr(hwnd, GWLP_USERDATA, (LONG_PTR)gui);
        } else {
            gui = (BO3ModGUI*)GetWindowLongPtr(hwnd, GWLP_USERDATA);
        }
        
        if (gui) {
            switch (uMsg) {
            case WM_COMMAND:
                gui->HandleCommand(wParam);
                break;
                
            case WM_SIZE:
                SendMessage(gui->hStatusBar, WM_SIZE, 0, 0);
                break;
                
            case WM_CLOSE:
                if (gui->isInjected) {
                    int result = MessageBox(hwnd, 
                        L"DLL is still injected. Eject before closing?", 
                        L"Confirm Exit", MB_YESNOCANCEL | MB_ICONQUESTION);
                    
                    if (result == IDYES) {
                        gui->EjectDLL();
                    } else if (result == IDCANCEL) {
                        return 0;
                    }
                }
                DestroyWindow(hwnd);
                break;
                
            case WM_DESTROY:
                PostQuitMessage(0);
                break;
                
            default:
                return DefWindowProc(hwnd, uMsg, wParam, lParam);
            }
        } else {
            return DefWindowProc(hwnd, uMsg, wParam, lParam);
        }
        
        return 0;
    }
    
    void Run() {
        MSG msg;
        while (GetMessage(&msg, nullptr, 0, 0)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
    }
};

// Control IDs
enum {
    ID_PROCESS_LIST = 1001,
    ID_REFRESH_PROCESSES,
    ID_BROWSE_DLL,
    ID_INJECT,
    ID_EJECT,
    ID_ESP_CHECK,
    ID_AIMBOT_CHECK,
    ID_INFO_CHECK,
    ID_ADVANCED_ESP_CHECK,
    ID_RADAR_CHECK,
    ID_TRIGGER_CHECK
};

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    InitCommonControls();
    
    BO3ModGUI gui;
    if (!gui.Initialize(hInstance)) {
        MessageBox(nullptr, L"Failed to initialize GUI!", L"Error", MB_OK | MB_ICONERROR);
        return 1;
    }
    
    gui.Run();
    return 0;
}
