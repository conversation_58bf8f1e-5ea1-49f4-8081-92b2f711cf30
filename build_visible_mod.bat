@echo off
echo Building Visible BO3 Mod...
echo.

REM Find Visual Studio
set VCVARS=""

if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    set VCVARS="C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    goto build
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    set VCVARS="C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
    goto build
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat" (
    set VCVARS="C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat"
    goto build
)

echo Visual Studio 2022 not found!
pause
exit /b 1

:build
echo Found Visual Studio at: %VCVARS%
echo.

REM Initialize Visual Studio environment
call %VCVARS%

echo Building VisibleMod.dll...
cl /LD /EHsc VisibleMod.cpp /Fe:VisibleMod.dll /link kernel32.lib user32.lib gdi32.lib

if errorlevel 1 (
    echo.
    echo Failed to build VisibleMod.dll
    pause
    exit /b 1
)

echo Building VisibleInjector.exe...
cl /EHsc VisibleInjector.cpp /Fe:VisibleInjector.exe /link kernel32.lib

if errorlevel 1 (
    echo.
    echo Failed to build VisibleInjector.exe
    pause
    exit /b 1
)

echo.
echo ✅ Build successful!
echo.
echo Files created:
if exist VisibleMod.dll echo ✓ VisibleMod.dll
if exist VisibleInjector.exe echo ✓ VisibleInjector.exe
echo.

echo This version will create:
echo - A visible console window
echo - Message boxes for feedback
echo - A status window
echo - A log file (BO3_Mod_Log.txt)
echo.

echo Ready to inject! Run: VisibleInjector.exe
echo.
pause
