#include <Windows.h>
#include <iostream>
#include <thread>
#include <atomic>

// Simple mod state
std::atomic<bool> g_espEnabled{false};
std::atomic<bool> g_aimbotEnabled{false};
std::atomic<bool> g_infoEnabled{false};
std::atomic<bool> g_running{true};

// Console for output
void AllocateConsole() {
    AllocConsole();
    freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
    freopen_s((FILE**)stderr, "CONOUT$", "w", stderr);
    freopen_s((FILE**)stdin, "CONIN$", "r", stdin);
    
    SetConsoleTitleA("BO3 Simple Mod - Educational Demo");
}

// Simple message box notifications
void ShowNotification(const char* title, const char* message) {
    MessageBoxA(nullptr, message, title, MB_OK | MB_ICONINFORMATION | MB_TOPMOST);
}

// Hotkey handler thread
void HotkeyThread() {
    std::cout << "========================================\n";
    std::cout << "BO3 Simple Mod - Educational Demo\n";
    std::cout << "========================================\n";
    std::cout << "Mod successfully injected!\n";
    std::cout << "\nControls:\n";
    std::cout << "F1 - Toggle ESP\n";
    std::cout << "F2 - Toggle Aimbot\n";
    std::cout << "F3 - Toggle Info Display\n";
    std::cout << "ESC - Exit mod\n";
    std::cout << "\nWARNING: Educational use only!\n";
    std::cout << "Never use in online multiplayer!\n";
    std::cout << "========================================\n\n";
    
    while (g_running) {
        // Check for hotkeys
        if (GetAsyncKeyState(VK_F1) & 0x8000) {
            g_espEnabled = !g_espEnabled;
            std::cout << "[F1] ESP " << (g_espEnabled ? "ENABLED" : "DISABLED") << std::endl;
            ShowNotification("BO3 Mod", g_espEnabled ? "ESP Enabled" : "ESP Disabled");
            Sleep(200); // Debounce
        }
        
        if (GetAsyncKeyState(VK_F2) & 0x8000) {
            g_aimbotEnabled = !g_aimbotEnabled;
            std::cout << "[F2] Aimbot " << (g_aimbotEnabled ? "ENABLED" : "DISABLED") << std::endl;
            ShowNotification("BO3 Mod", g_aimbotEnabled ? "Aimbot Enabled" : "Aimbot Disabled");
            Sleep(200); // Debounce
        }
        
        if (GetAsyncKeyState(VK_F3) & 0x8000) {
            g_infoEnabled = !g_infoEnabled;
            std::cout << "[F3] Info Display " << (g_infoEnabled ? "ENABLED" : "DISABLED") << std::endl;
            ShowNotification("BO3 Mod", g_infoEnabled ? "Info Display Enabled" : "Info Display Disabled");
            Sleep(200); // Debounce
        }
        
        if (GetAsyncKeyState(VK_ESCAPE) & 0x8000) {
            std::cout << "\n[ESC] Exiting mod...\n";
            ShowNotification("BO3 Mod", "Mod is exiting...");
            g_running = false;
            break;
        }
        
        Sleep(50); // Check every 50ms
    }
    
    std::cout << "Mod thread exiting...\n";
}

// Status display thread
void StatusThread() {
    int counter = 0;
    while (g_running) {
        if (counter % 100 == 0) { // Every 5 seconds
            std::cout << "\n--- Mod Status ---\n";
            std::cout << "ESP: " << (g_espEnabled ? "ON" : "OFF") << "\n";
            std::cout << "Aimbot: " << (g_aimbotEnabled ? "ON" : "OFF") << "\n";
            std::cout << "Info: " << (g_infoEnabled ? "ON" : "OFF") << "\n";
            std::cout << "Runtime: " << (counter * 50 / 1000) << " seconds\n";
            std::cout << "-------------------\n\n";
        }
        counter++;
        Sleep(50);
    }
}

// DLL Entry Point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        // Disable DLL_THREAD_ATTACH and DLL_THREAD_DETACH notifications
        DisableThreadLibraryCalls(hModule);
        
        // Allocate console for output
        AllocateConsole();
        
        // Start hotkey handler thread
        std::thread(HotkeyThread).detach();
        
        // Start status display thread
        std::thread(StatusThread).detach();
        
        // Show initial notification
        ShowNotification("BO3 Mod Injected", 
                        "Educational mod successfully loaded!\n\n"
                        "Controls:\n"
                        "F1 - Toggle ESP\n"
                        "F2 - Toggle Aimbot\n" 
                        "F3 - Toggle Info\n"
                        "ESC - Exit\n\n"
                        "WARNING: Educational use only!");
        break;
        
    case DLL_PROCESS_DETACH:
        g_running = false;
        
        // Show exit notification
        ShowNotification("BO3 Mod", "Mod has been unloaded.");
        
        // Free console
        FreeConsole();
        break;
    }
    return TRUE;
}

// Export functions for external control
extern "C" {
    __declspec(dllexport) void ToggleESP() {
        g_espEnabled = !g_espEnabled;
        std::cout << "[API] ESP " << (g_espEnabled ? "ENABLED" : "DISABLED") << std::endl;
    }
    
    __declspec(dllexport) void ToggleAimbot() {
        g_aimbotEnabled = !g_aimbotEnabled;
        std::cout << "[API] Aimbot " << (g_aimbotEnabled ? "ENABLED" : "DISABLED") << std::endl;
    }
    
    __declspec(dllexport) void ToggleInfo() {
        g_infoEnabled = !g_infoEnabled;
        std::cout << "[API] Info Display " << (g_infoEnabled ? "ENABLED" : "DISABLED") << std::endl;
    }
    
    __declspec(dllexport) bool IsESPEnabled() {
        return g_espEnabled;
    }
    
    __declspec(dllexport) bool IsAimbotEnabled() {
        return g_aimbotEnabled;
    }
    
    __declspec(dllexport) bool IsInfoEnabled() {
        return g_infoEnabled;
    }
}
