# BO3 Mod Framework - Quick Start

## ⚠️ IMPORTANT WARNINGS
- **EDUCATIONAL PURPOSE ONLY**
- **NEVER USE ONLINE** - You will be banned
- **OFFLINE TESTING ONLY**

## Quick Setup

1. **Verify Game Version**: Black Ops 3 version 37.1.1.0
2. **Build Framework**:
   ```
   cd build_tools
   build.bat
   ```
3. **Validate Setup**:
   ```
   cd validation
   python validate_framework.py
   ```
4. **Test Framework**:
   ```
   cd examples
   # Compile and test example mod
   ```

## File Structure
- `src/` - Core framework source code
- `docs/` - Complete documentation
- `examples/` - Example mod implementations
- `build_tools/` - Build system files
- `validation/` - Testing tools

## Next Steps
1. Read `docs/README.md` for complete overview
2. Study `docs/TECHNICAL_DETAILS.md` for technical info
3. Follow `docs/USAGE_GUIDE.md` for detailed instructions
4. Start with `examples/ExampleMod.cpp` to understand the framework

## Support
- Validate your setup regularly
- Read all documentation carefully
- Use only in educational/offline environments
- Respect game terms of service

**Remember: This is a learning tool for understanding game modding concepts!**
