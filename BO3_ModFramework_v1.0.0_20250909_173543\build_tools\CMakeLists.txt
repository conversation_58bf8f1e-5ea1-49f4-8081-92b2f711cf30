cmake_minimum_required(VERSION 3.16)
project(BO3ModFramework)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set build type to Release by default
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler-specific options
if(MSVC)
    # Enable multi-processor compilation
    add_compile_options(/MP)
    
    # Set runtime library
    set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>")
    
    # Disable specific warnings
    add_compile_options(/wd4996)  # Disable deprecated function warnings
    
    # Enable security features
    add_compile_options(/GS /sdl)
endif()

# Platform check - this framework is Windows-only
if(NOT WIN32)
    message(FATAL_ERROR "This framework is designed for Windows only")
endif()

# Architecture check - BO3 is 64-bit only
if(NOT CMAKE_SIZEOF_VOID_P EQUAL 8)
    message(FATAL_ERROR "This framework requires 64-bit compilation (BO3 is 64-bit)")
endif()

# Find required packages
find_package(DirectX QUIET)

# DirectX libraries (fallback if not found by find_package)
set(DIRECTX_LIBRARIES
    d3d9.lib
    d3dx9.lib
    dxguid.lib
)

# Windows libraries
set(WINDOWS_LIBRARIES
    kernel32.lib
    user32.lib
    gdi32.lib
    winspool.lib
    comdlg32.lib
    advapi32.lib
    shell32.lib
    ole32.lib
    oleaut32.lib
    uuid.lib
    odbc32.lib
    odbccp32.lib
)

# ========== MOD FRAMEWORK LIBRARY ==========
add_library(BO3ModFramework STATIC
    BO3_ModFramework.h
    BO3_ModFramework.cpp
)

target_include_directories(BO3ModFramework PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
)

target_link_libraries(BO3ModFramework
    ${DIRECTX_LIBRARIES}
    ${WINDOWS_LIBRARIES}
)

# Set library properties
set_target_properties(BO3ModFramework PROPERTIES
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
)

# ========== EXAMPLE MOD DLL ==========
add_library(ExampleMod SHARED
    ExampleMod.cpp
)

target_link_libraries(ExampleMod
    BO3ModFramework
    ${DIRECTX_LIBRARIES}
    ${WINDOWS_LIBRARIES}
)

# Set DLL properties
set_target_properties(ExampleMod PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    OUTPUT_NAME "BO3ExampleMod"
    PREFIX ""  # Remove lib prefix on Unix-like systems
)

# Export symbols for DLL
if(MSVC)
    set_target_properties(ExampleMod PROPERTIES
        WINDOWS_EXPORT_ALL_SYMBOLS TRUE
    )
endif()

# ========== INJECTOR EXECUTABLE ==========
add_executable(Injector
    Injector.cpp
)

target_link_libraries(Injector
    ${WINDOWS_LIBRARIES}
)

set_target_properties(Injector PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    OUTPUT_NAME "BO3Injector"
)

# ========== BUILD TEST EXECUTABLE ==========
add_executable(BuildTest
    test_build.cpp
)

target_link_libraries(BuildTest
    BO3ModFramework
    ${DIRECTX_LIBRARIES}
    ${WINDOWS_LIBRARIES}
)

set_target_properties(BuildTest PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    OUTPUT_NAME "BO3BuildTest"
)

# ========== INSTALLATION ==========
install(TARGETS BO3ModFramework ExampleMod Injector
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES
    BO3_ModFramework.h
    README.md
    DESTINATION include
)

# ========== CUSTOM TARGETS ==========

# Clean build artifacts
add_custom_target(clean-all
    COMMAND ${CMAKE_COMMAND} -E remove_directory "${CMAKE_BINARY_DIR}/bin"
    COMMAND ${CMAKE_COMMAND} -E remove_directory "${CMAKE_BINARY_DIR}/lib"
    COMMAND ${CMAKE_COMMAND} -E remove_directory "${CMAKE_BINARY_DIR}/CMakeFiles"
    COMMENT "Cleaning all build artifacts"
)

# Package for distribution
add_custom_target(package
    COMMAND ${CMAKE_COMMAND} -E make_directory "${CMAKE_BINARY_DIR}/package"
    COMMAND ${CMAKE_COMMAND} -E copy "${CMAKE_BINARY_DIR}/bin/BO3ExampleMod.dll" "${CMAKE_BINARY_DIR}/package/"
    COMMAND ${CMAKE_COMMAND} -E copy "${CMAKE_BINARY_DIR}/bin/BO3Injector.exe" "${CMAKE_BINARY_DIR}/package/"
    COMMAND ${CMAKE_COMMAND} -E copy "${CMAKE_SOURCE_DIR}/README.md" "${CMAKE_BINARY_DIR}/package/"
    COMMAND ${CMAKE_COMMAND} -E copy "${CMAKE_SOURCE_DIR}/BO3_ModFramework.h" "${CMAKE_BINARY_DIR}/package/"
    DEPENDS ExampleMod Injector
    COMMENT "Creating distribution package"
)

# ========== COMPILER DEFINITIONS ==========
target_compile_definitions(BO3ModFramework PRIVATE
    WIN32_LEAN_AND_MEAN
    NOMINMAX
    _CRT_SECURE_NO_WARNINGS
)

target_compile_definitions(ExampleMod PRIVATE
    WIN32_LEAN_AND_MEAN
    NOMINMAX
    _CRT_SECURE_NO_WARNINGS
)

target_compile_definitions(Injector PRIVATE
    WIN32_LEAN_AND_MEAN
    NOMINMAX
    _CRT_SECURE_NO_WARNINGS
)

# ========== DEBUG CONFIGURATION ==========
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(BO3ModFramework PRIVATE DEBUG _DEBUG)
    target_compile_definitions(ExampleMod PRIVATE DEBUG _DEBUG)
    target_compile_definitions(Injector PRIVATE DEBUG _DEBUG)
    
    # Enable debug symbols
    if(MSVC)
        target_compile_options(BO3ModFramework PRIVATE /Zi)
        target_compile_options(ExampleMod PRIVATE /Zi)
        target_compile_options(Injector PRIVATE /Zi)
        
        target_link_options(ExampleMod PRIVATE /DEBUG)
        target_link_options(Injector PRIVATE /DEBUG)
    endif()
endif()

# ========== RELEASE CONFIGURATION ==========
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    target_compile_definitions(BO3ModFramework PRIVATE NDEBUG)
    target_compile_definitions(ExampleMod PRIVATE NDEBUG)
    target_compile_definitions(Injector PRIVATE NDEBUG)
    
    # Optimization flags
    if(MSVC)
        target_compile_options(BO3ModFramework PRIVATE /O2 /Ob2)
        target_compile_options(ExampleMod PRIVATE /O2 /Ob2)
        target_compile_options(Injector PRIVATE /O2 /Ob2)
    endif()
endif()

# ========== INFORMATION ==========
message(STATUS "=== BO3 Mod Framework Configuration ===")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Compiler: ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "Architecture: ${CMAKE_SYSTEM_PROCESSOR}")
message(STATUS "CMake version: ${CMAKE_VERSION}")
message(STATUS "========================================")

# Warning about usage
message(WARNING "This framework is for educational purposes only!")
message(WARNING "Using mods in online games may result in bans!")
message(WARNING "Use only in offline/private environments!")
