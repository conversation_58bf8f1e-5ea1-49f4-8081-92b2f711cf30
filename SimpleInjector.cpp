#include <Windows.h>
#include <TlHelp32.h>
#include <iostream>
#include <string>

class SimpleInjector {
public:
    static DWORD FindProcess(const std::wstring& processName) {
        HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (snapshot == INVALID_HANDLE_VALUE) return 0;
        
        PROCESSENTRY32W pe;
        pe.dwSize = sizeof(pe);
        
        if (Process32FirstW(snapshot, &pe)) {
            do {
                if (processName == pe.szExeFile) {
                    CloseHandle(snapshot);
                    return pe.th32ProcessID;
                }
            } while (Process32NextW(snapshot, &pe));
        }
        
        CloseHandle(snapshot);
        return 0;
    }
    
    static bool InjectDLL(DWORD processId, const std::string& dllPath) {
        HANDLE process = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
        if (!process) {
            std::cout << "Failed to open process. Error: " << GetLastError() << std::endl;
            return false;
        }
        
        // Allocate memory in target process
        LPVOID allocatedMemory = VirtualAllocEx(process, nullptr, dllPath.length() + 1,
                                               MEM_RESERVE | MEM_COMMIT, PAGE_READWRITE);
        if (!allocatedMemory) {
            std::cout << "Failed to allocate memory. Error: " << GetLastError() << std::endl;
            CloseHandle(process);
            return false;
        }
        
        // Write DLL path to allocated memory
        if (!WriteProcessMemory(process, allocatedMemory, dllPath.c_str(), dllPath.length() + 1, nullptr)) {
            std::cout << "Failed to write memory. Error: " << GetLastError() << std::endl;
            VirtualFreeEx(process, allocatedMemory, 0, MEM_RELEASE);
            CloseHandle(process);
            return false;
        }
        
        // Get LoadLibraryA address
        HMODULE kernel32 = GetModuleHandleA("kernel32.dll");
        FARPROC loadLibraryAddr = GetProcAddress(kernel32, "LoadLibraryA");
        
        // Create remote thread
        HANDLE remoteThread = CreateRemoteThread(process, nullptr, 0,
                                                (LPTHREAD_START_ROUTINE)loadLibraryAddr,
                                                allocatedMemory, 0, nullptr);
        if (!remoteThread) {
            std::cout << "Failed to create remote thread. Error: " << GetLastError() << std::endl;
            VirtualFreeEx(process, allocatedMemory, 0, MEM_RELEASE);
            CloseHandle(process);
            return false;
        }
        
        // Wait for injection to complete
        WaitForSingleObject(remoteThread, INFINITE);
        
        // Cleanup
        CloseHandle(remoteThread);
        VirtualFreeEx(process, allocatedMemory, 0, MEM_RELEASE);
        CloseHandle(process);
        
        return true;
    }
};

int main(int argc, char* argv[]) {
    std::cout << "========================================\n";
    std::cout << "BO3 Simple DLL Injector\n";
    std::cout << "========================================\n";
    std::cout << "WARNING: Educational use only!\n";
    std::cout << "Never use in online multiplayer!\n";
    std::cout << "========================================\n\n";
    
    std::string dllPath = "SimpleMod.dll";
    if (argc > 1) {
        dllPath = argv[1];
    }
    
    // Check if DLL exists
    if (GetFileAttributesA(dllPath.c_str()) == INVALID_FILE_ATTRIBUTES) {
        std::cout << "DLL file not found: " << dllPath << std::endl;
        std::cout << "Press Enter to exit...";
        std::cin.get();
        return 1;
    }
    
    // Find BlackOps3.exe
    std::cout << "Looking for BlackOps3.exe...\n";
    DWORD processId = SimpleInjector::FindProcess(L"BlackOps3.exe");
    
    if (processId == 0) {
        std::cout << "BlackOps3.exe not found!\n";
        std::cout << "Make sure the game is running.\n";
        std::cout << "Press Enter to exit...";
        std::cin.get();
        return 1;
    }
    
    std::cout << "Found BlackOps3.exe (PID: " << processId << ")\n";
    std::cout << "Injecting DLL: " << dllPath << "\n";
    
    if (SimpleInjector::InjectDLL(processId, dllPath)) {
        std::cout << "\n✅ DLL injected successfully!\n";
        std::cout << "\nControls:\n";
        std::cout << "F1 - Toggle ESP\n";
        std::cout << "F2 - Toggle Aimbot\n";
        std::cout << "F3 - Toggle Info Display\n";
        std::cout << "ESC - Exit\n";
        std::cout << "\nPress Enter to exit injector...";
        std::cin.get();
    } else {
        std::cout << "\n❌ DLL injection failed!\n";
        std::cout << "Make sure you're running as Administrator.\n";
        std::cout << "Press Enter to exit...";
        std::cin.get();
        return 1;
    }
    
    return 0;
}
