@echo off
echo Building BO3 GUI with MSBuild...
echo.

REM Try to find MSBuild
set MSBUILD_PATH=""

REM Check Visual Studio 2022 Community
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    goto :build
)

REM Check Visual Studio 2022 Professional
if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
    goto :build
)

REM Check Visual Studio 2022 Enterprise
if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
    goto :build
)

REM Check standalone MSBuild
if exist "C:\Program Files\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\MSBuild\Current\Bin\MSBuild.exe"
    goto :build
)

echo MSBuild not found! Please install Visual Studio 2022 or MSBuild Tools.
pause
exit /b 1

:build
echo Found MSBuild at: %MSBUILD_PATH%
echo.

echo Building BO3_GUI.sln...
%MSBUILD_PATH% BO3_GUI.sln /p:Configuration=Release /p:Platform=x64 /v:minimal

if errorlevel 1 (
    echo.
    echo Build failed! Check the error messages above.
    pause
    exit /b 1
)

echo.
echo Build successful!
echo.

if exist "bin\Release\BO3_GUI.exe" (
    echo ✓ BO3_GUI.exe created successfully!
    echo.
    echo You can now run: bin\Release\BO3_GUI.exe
    echo.
    set /p choice="Do you want to run the GUI now? (y/n): "
    if /i "%choice%"=="y" (
        echo Starting BO3_GUI...
        start bin\Release\BO3_GUI.exe
    )
) else (
    echo ❌ BO3_GUI.exe not found in expected location.
    echo Check the build output above for errors.
)

echo.
pause
