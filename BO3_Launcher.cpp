#include <Windows.h>
#include <iostream>
#include <string>

// ========== BO3 MOD LAUNCHER ==========
// Simple console-based launcher for the BO3 modding framework

void PrintBanner() {
    std::cout << "========================================\n";
    std::cout << "Call of Duty: Black Ops 3 Mod Framework\n";
    std::cout << "========================================\n";
    std::cout << "Version: 1.0.0 Advanced\n";
    std::cout << "Target Game Version: ********\n";
    std::cout << "\n";
    std::cout << "WARNING: Educational use only!\n";
    std::cout << "Never use in online multiplayer!\n";
    std::cout << "========================================\n\n";
}

void PrintMenu() {
    std::cout << "Select an option:\n";
    std::cout << "1. Launch GUI Control Panel\n";
    std::cout << "2. Quick Inject (Console Mode)\n";
    std::cout << "3. Validate Framework\n";
    std::cout << "4. Build Framework\n";
    std::cout << "5. View Documentation\n";
    std::cout << "6. Exit\n";
    std::cout << "\nChoice: ";
}

void LaunchGUI() {
    std::cout << "Launching GUI Control Panel...\n";
    
    // Check if GUI executable exists
    if (GetFileAttributesA("BO3_GUI.exe") != INVALID_FILE_ATTRIBUTES) {
        ShellExecuteA(nullptr, "open", "BO3_GUI.exe", nullptr, nullptr, SW_SHOW);
    } else {
        std::cout << "GUI executable not found. Building...\n";
        system("g++ -o BO3_GUI.exe BO3_GUI.cpp -lcomctl32 -lgdi32 -luser32 -lkernel32");
        
        if (GetFileAttributesA("BO3_GUI.exe") != INVALID_FILE_ATTRIBUTES) {
            ShellExecuteA(nullptr, "open", "BO3_GUI.exe", nullptr, nullptr, SW_SHOW);
        } else {
            std::cout << "Failed to build GUI. Please compile manually.\n";
        }
    }
}

void QuickInject() {
    std::cout << "Quick Inject Mode\n";
    std::cout << "================\n";
    
    // Check if injector exists
    if (GetFileAttributesA("BO3Injector.exe") == INVALID_FILE_ATTRIBUTES) {
        std::cout << "Injector not found. Please build the framework first.\n";
        return;
    }
    
    // Check if mod DLL exists
    if (GetFileAttributesA("BO3ExampleMod.dll") == INVALID_FILE_ATTRIBUTES) {
        std::cout << "Mod DLL not found. Please build the framework first.\n";
        return;
    }
    
    std::cout << "Starting injector...\n";
    std::cout << "\nControls after injection:\n";
    std::cout << "F1 - Toggle ESP\n";
    std::cout << "F2 - Toggle Aimbot\n";
    std::cout << "F3 - Toggle Info Display\n";
    std::cout << "F4 - Toggle Predictive ESP\n";
    std::cout << "F5 - Toggle Radar Hack\n";
    std::cout << "F6 - Toggle Trigger Bot\n";
    std::cout << "ESC - Exit injector\n\n";
    
    system("BO3Injector.exe BO3ExampleMod.dll");
}

void ValidateFramework() {
    std::cout << "Validating Framework...\n";
    std::cout << "======================\n";
    
    if (GetFileAttributesA("build/validate_framework.py") != INVALID_FILE_ATTRIBUTES) {
        system("cd build && python validate_framework.py");
    } else {
        std::cout << "Validator not found. Checking files manually...\n";
        
        // Check essential files
        const char* essentialFiles[] = {
            "BO3_ModFramework.h",
            "BO3_ModFramework.cpp",
            "ExampleMod.cpp",
            "AdvancedExampleMod.cpp",
            "BO3_AdvancedExtensions.h",
            "BO3_AdvancedExtensions.cpp",
            "Injector.cpp",
            "CMakeLists.txt",
            "README.md"
        };
        
        int foundFiles = 0;
        int totalFiles = sizeof(essentialFiles) / sizeof(essentialFiles[0]);
        
        for (int i = 0; i < totalFiles; i++) {
            if (GetFileAttributesA(essentialFiles[i]) != INVALID_FILE_ATTRIBUTES) {
                std::cout << "✓ " << essentialFiles[i] << "\n";
                foundFiles++;
            } else {
                std::cout << "✗ " << essentialFiles[i] << " (missing)\n";
            }
        }
        
        std::cout << "\nValidation Result: " << foundFiles << "/" << totalFiles << " files found\n";
        
        if (foundFiles == totalFiles) {
            std::cout << "✅ Framework validation passed!\n";
        } else {
            std::cout << "❌ Framework validation failed. Some files are missing.\n";
        }
    }
}

void BuildFramework() {
    std::cout << "Building Framework...\n";
    std::cout << "====================\n";
    
    // Try different build methods
    std::cout << "Attempting to build with available tools...\n";
    
    // Method 1: CMake
    if (system("cmake --version > nul 2>&1") == 0) {
        std::cout << "Using CMake build system...\n";
        system("mkdir build 2>nul");
        system("cd build && cmake .. -G \"Visual Studio 17 2022\" -A x64");
        system("cd build && cmake --build . --config Release");
    }
    // Method 2: Make
    else if (system("make --version > nul 2>&1") == 0) {
        std::cout << "Using Make build system...\n";
        system("make all");
    }
    // Method 3: Direct compilation
    else {
        std::cout << "Using direct compilation...\n";
        
        // Build framework library
        std::cout << "Building framework library...\n";
        system("g++ -c BO3_ModFramework.cpp -o BO3_ModFramework.o");
        system("g++ -c BO3_AdvancedExtensions.cpp -o BO3_AdvancedExtensions.o");
        system("ar rcs libBO3ModFramework.a BO3_ModFramework.o BO3_AdvancedExtensions.o");
        
        // Build example mod DLL
        std::cout << "Building example mod DLL...\n";
        system("g++ -shared -o BO3ExampleMod.dll ExampleMod.cpp libBO3ModFramework.a -ld3d9 -ld3dx9");
        
        // Build advanced mod DLL
        std::cout << "Building advanced mod DLL...\n";
        system("g++ -shared -o BO3AdvancedMod.dll AdvancedExampleMod.cpp libBO3ModFramework.a -ld3d9 -ld3dx9");
        
        // Build injector
        std::cout << "Building injector...\n";
        system("g++ -o BO3Injector.exe Injector.cpp -lkernel32 -luser32");
        
        // Build GUI
        std::cout << "Building GUI...\n";
        system("g++ -o BO3_GUI.exe BO3_GUI.cpp -lcomctl32 -lgdi32 -luser32 -lkernel32");
    }
    
    std::cout << "\nBuild completed. Check for any error messages above.\n";
}

void ViewDocumentation() {
    std::cout << "Available Documentation:\n";
    std::cout << "=======================\n";
    std::cout << "1. README.md - General overview and getting started\n";
    std::cout << "2. TECHNICAL_DETAILS.md - Deep technical information\n";
    std::cout << "3. USAGE_GUIDE.md - Step-by-step usage instructions\n";
    std::cout << "4. ADVANCED_FEATURES.md - Advanced modding capabilities\n";
    std::cout << "\nSelect document to view (1-4): ";
    
    int choice;
    std::cin >> choice;
    
    const char* docs[] = {
        "README.md",
        "TECHNICAL_DETAILS.md", 
        "USAGE_GUIDE.md",
        "ADVANCED_FEATURES.md"
    };
    
    if (choice >= 1 && choice <= 4) {
        std::string command = "notepad ";
        command += docs[choice - 1];
        system(command.c_str());
    } else {
        std::cout << "Invalid choice.\n";
    }
}

int main() {
    PrintBanner();
    
    int choice;
    do {
        PrintMenu();
        std::cin >> choice;
        std::cout << "\n";
        
        switch (choice) {
        case 1:
            LaunchGUI();
            break;
        case 2:
            QuickInject();
            break;
        case 3:
            ValidateFramework();
            break;
        case 4:
            BuildFramework();
            break;
        case 5:
            ViewDocumentation();
            break;
        case 6:
            std::cout << "Exiting...\n";
            break;
        default:
            std::cout << "Invalid choice. Please try again.\n";
        }
        
        if (choice != 6) {
            std::cout << "\nPress Enter to continue...";
            std::cin.ignore();
            std::cin.get();
            system("cls");
            PrintBanner();
        }
        
    } while (choice != 6);
    
    return 0;
}
