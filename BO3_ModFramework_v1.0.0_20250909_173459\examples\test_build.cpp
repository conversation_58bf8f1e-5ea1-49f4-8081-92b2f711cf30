// Simple test to verify build environment
#include "BO3_ModFramework.h"
#include <iostream>

int main() {
    std::cout << "BO3 Mod Framework Build Test" << std::endl;
    std::cout << "Testing basic functionality..." << std::endl;
    
    // Test basic vector operations
    Vec3 testVec = {1.0f, 2.0f, 3.0f};
    std::cout << "Vec3 test: (" << testVec.x << ", " << testVec.y << ", " << testVec.z << ")" << std::endl;
    
    // Test distance calculation
    Vec3 pos1 = {0.0f, 0.0f, 0.0f};
    Vec3 pos2 = {3.0f, 4.0f, 0.0f};
    float distance = GetDistance(pos1, pos2);
    std::cout << "Distance test: " << distance << " (expected: 5.0)" << std::endl;
    
    std::cout << "Build test completed successfully!" << std::endl;
    return 0;
}
