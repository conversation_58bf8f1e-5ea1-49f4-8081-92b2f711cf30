#include "BO3_AdvancedExtensions.h"
#include <iostream>
#include <vector>
#include <string>
#include <cstdio>
#include <cmath>

// ========== ADVANCED BO3 MOD WITH NEW FEATURES ==========

class AdvancedBO3Mod {
private:
    bool predictiveESP;
    bool wallHackESP;
    bool velocityESP;
    bool advancedAimbot;
    bool silentAim;
    bool triggerBot;
    bool radarHack;
    bool healthESP;
    bool clanTagESP;
    
public:
    AdvancedBO3Mod() : predictiveESP(true), wallHackESP(true), velocityESP(true),
                       advancedAimbot(false), silentAim(false), triggerBot(false),
                       radarHack(true), healthESP(true), clanTagESP(true) {}
    
    void Update() {
        if (!IsInGame()) return;
        
        if (predictiveESP || wallHackESP || velocityESP) {
            DrawAdvancedESP();
        }
        
        if (radarHack) {
            DrawRadarHack();
        }
        
        if (advancedAimbot) {
            UpdateAdvancedAimbot();
        }
        
        if (silentAim) {
            UpdateSilentAim();
        }
        
        if (triggerBot) {
            UpdateTriggerBot();
        }
        
        DrawAdvancedInfo();
    }
    
    void DrawAdvancedESP() {
        CG_T* cgt = CG_GetCGT(0);
        if (!cgt) return;
        
        refDef_t* refdef = &cgt->refdef;
        if (!refdef) return;
        
        // Get local player info
        clientInfo_t* localClient = &cgt->clientInfo[0];
        int localTeam = localClient->iTeam;
        
        // Colors for different states
        float enemyColor[4] = {1.0f, 0.0f, 0.0f, 1.0f};      // Red
        float friendlyColor[4] = {0.0f, 1.0f, 0.0f, 1.0f};   // Green
        float spectatorColor[4] = {1.0f, 1.0f, 0.0f, 1.0f};  // Yellow
        float deadColor[4] = {0.5f, 0.5f, 0.5f, 1.0f};       // Gray
        
        // Loop through all entities
        for (int i = 1; i < 18; i++) {
            centity_t* entity = &cgt->centityArray[i];
            if (!IsValidEntity(entity)) continue;
            
            clientInfo_t* client = &cgt->clientInfo[i];
            if (!IsValidClientInfo(client)) continue;
            
            // Determine color based on team and state
            float* color = friendlyColor;
            if (!IsPlayerAlive(i)) {
                color = deadColor;
            } else if (IsSpectator(i)) {
                color = spectatorColor;
            } else if (!IsSameTeam(0, i)) {
                color = enemyColor;
            }
            
            // Draw different ESP types
            if (wallHackESP) {
                DrawWallHackESP(entity, client, color, refdef);
            }
            
            if (predictiveESP) {
                DrawPredictiveESP(entity, client, color, refdef);
            }
            
            if (velocityESP) {
                DrawVelocityESP(entity, client, color, refdef);
            }
            
            if (healthESP) {
                DrawHealthESP(entity, client, color, refdef);
            }
            
            if (clanTagESP) {
                DrawClanTagESP(entity, client, color, refdef);
            }
        }
    }
    
    void DrawWallHackESP(centity_t* entity, clientInfo_t* client, float* color, refDef_t* refdef) {
        Vec3 headPos = GetBonePosition((CEntity*)entity, "j_head");
        if (headPos.x == 0 && headPos.y == 0 && headPos.z == 0) {
            headPos = entity->vOrigin;
            headPos.z += 60.0f;
        }
        
        Vec2 screenPos;
        if (WorldToScreenAdvanced(headPos, &screenPos, refdef)) {
            // Check if behind wall
            bool behindWall = IsEntityBehindWall(entity, refdef->vViewOrigin);
            
            char playerInfo[256];
            sprintf_s(playerInfo, "%s [%.0fm]%s", 
                     client->cName, 
                     GetEntityDistance3D(entity, refdef->vViewOrigin),
                     behindWall ? " [WALL]" : "");
            
            // Adjust alpha based on visibility
            float alpha = behindWall ? 0.6f : 1.0f;
            
            DrawText(playerInfo, screenPos.x - 50, screenPos.y - 30, 0.8f, 0.8f);
        }
    }
    
    void DrawPredictiveESP(centity_t* entity, clientInfo_t* client, float* color, refDef_t* refdef) {
        // Predict where the player will be in 0.5 seconds
        Vec3 predictedPos = PredictEntityPosition(entity, 0.5f);
        
        Vec2 currentScreen, predictedScreen;
        if (WorldToScreenAdvanced(entity->vOrigin, &currentScreen, refdef) &&
            WorldToScreenAdvanced(predictedPos, &predictedScreen, refdef)) {
            
            // Draw line from current to predicted position
            // DrawLine(currentScreen, predictedScreen, color); // Would need line drawing implementation
            
            // Draw predicted position marker
            char predictionText[64];
            sprintf_s(predictionText, "PRED");
            DrawText(predictionText, predictedScreen.x - 15, predictedScreen.y - 10, 0.6f, 0.6f);
        }
    }
    
    void DrawVelocityESP(centity_t* entity, clientInfo_t* client, float* color, refDef_t* refdef) {
        Vec3 velocity = GetEntityVelocity(entity);
        float speed = VectorLength(velocity);
        
        if (speed > 10.0f) { // Only show if moving
            Vec2 screenPos;
            if (WorldToScreenAdvanced(entity->vOrigin, &screenPos, refdef)) {
                char velocityText[64];
                sprintf_s(velocityText, "%.0f u/s", speed);
                DrawText(velocityText, screenPos.x - 25, screenPos.y + 20, 0.7f, 0.7f);
            }
        }
    }
    
    void DrawHealthESP(centity_t* entity, clientInfo_t* client, float* color, refDef_t* refdef) {
        int health = GetPlayerHealth(entity->nextState.iNumber);
        if (health > 0) {
            Vec2 screenPos;
            if (WorldToScreenAdvanced(entity->vOrigin, &screenPos, refdef)) {
                char healthText[32];
                sprintf_s(healthText, "HP: %d", health);
                
                // Color based on health
                float healthColor[4];
                if (health > 75) {
                    healthColor[0] = 0.0f; healthColor[1] = 1.0f; healthColor[2] = 0.0f; // Green
                } else if (health > 25) {
                    healthColor[0] = 1.0f; healthColor[1] = 1.0f; healthColor[2] = 0.0f; // Yellow
                } else {
                    healthColor[0] = 1.0f; healthColor[1] = 0.0f; healthColor[2] = 0.0f; // Red
                }
                healthColor[3] = 1.0f;
                
                DrawText(healthText, screenPos.x - 20, screenPos.y + 35, 0.7f, 0.7f);
            }
        }
    }
    
    void DrawClanTagESP(centity_t* entity, clientInfo_t* client, float* color, refDef_t* refdef) {
        const char* clanTag = GetPlayerClanTag(entity->nextState.iNumber);
        if (strlen(clanTag) > 0) {
            Vec2 screenPos;
            if (WorldToScreenAdvanced(entity->vOrigin, &screenPos, refdef)) {
                char clanText[32];
                sprintf_s(clanText, "[%s]", clanTag);
                DrawText(clanText, screenPos.x - 30, screenPos.y - 45, 0.7f, 0.7f);
            }
        }
    }
    
    void DrawRadarHack() {
        CG_T* cgt = CG_GetCGT(0);
        if (!cgt) return;
        
        refDef_t* refdef = &cgt->refdef;
        Vec3 localPos = refdef->vViewOrigin;
        
        // Draw mini-radar in top-right corner
        float radarX = refdef->width - 150;
        float radarY = 50;
        float radarSize = 100;
        
        // Draw radar background
        // DrawFilledRect(radarX, radarY, radarSize, radarSize, 0.0f, 0.0f, 0.0f, 0.5f);
        
        // Draw players on radar
        for (int i = 1; i < 18; i++) {
            centity_t* entity = &cgt->centityArray[i];
            if (!IsValidEntity(entity)) continue;
            
            if (!IsPlayerAlive(i)) continue;
            
            Vec3 relativePos = VectorSubtract(entity->vOrigin, localPos);
            float distance = VectorLength(relativePos);
            
            if (distance > 1000.0f) continue; // Too far for radar
            
            // Convert to radar coordinates
            float radarPosX = radarX + radarSize/2 + (relativePos.x / 1000.0f) * (radarSize/2);
            float radarPosY = radarY + radarSize/2 - (relativePos.y / 1000.0f) * (radarSize/2);
            
            // Clamp to radar bounds
            radarPosX = max(radarX, min(radarX + radarSize, radarPosX));
            radarPosY = max(radarY, min(radarY + radarSize, radarPosY));
            
            // Draw dot for player
            char dot[2] = "•";
            if (IsSameTeam(0, i)) {
                // Green for teammates
                DrawText(dot, radarPosX, radarPosY, 0.8f, 0.8f);
            } else {
                // Red for enemies
                DrawText(dot, radarPosX, radarPosY, 0.8f, 0.8f);
            }
        }
    }
    
    void UpdateAdvancedAimbot() {
        if (!(GetAsyncKeyState(VK_RBUTTON) & 0x8000)) return;
        
        CG_T* cgt = CG_GetCGT(0);
        if (!cgt) return;
        
        centity_t* target = GetBestTarget(cgt);
        if (!target) return;
        
        // Get predicted target position
        Vec3 predictedPos = PredictEntityPosition(target, 0.2f); // Predict 200ms ahead
        Vec3 headPos = GetBonePosition((CEntity*)target, "j_head");
        if (headPos.x != 0 || headPos.y != 0 || headPos.z != 0) {
            predictedPos.z = headPos.z; // Use actual head height
        }
        
        // Calculate interception point for projectile weapons
        Vec3 shooterPos = cgt->refdef.vViewOrigin;
        Vec3 targetVel = GetEntityVelocity(target);
        Vec3 interceptPoint = CalculateInterceptionPoint(predictedPos, targetVel, shooterPos, 1000.0f); // Assume 1000 u/s projectile speed
        
        // Calculate angles to interception point
        Vec3 angles = VectorToAngles(VectorSubtract(interceptPoint, shooterPos));
        angles = NormalizeAngles(angles);
        
        // Apply smooth aiming (would need actual angle setting function)
        // SetViewAngles(angles);
    }
    
    void UpdateSilentAim() {
        if (!CL_GetUserCmd) return;
        
        usercmd_s* cmd = CL_GetUserCmd(-1); // Get latest command
        if (!cmd) return;
        
        if (cmd->iButtons & BUTTON_ATTACK) {
            CG_T* cgt = CG_GetCGT(0);
            if (!cgt) return;
            
            centity_t* target = GetBestTarget(cgt);
            if (target) {
                Vec3 headPos = GetBonePosition((CEntity*)target, "j_head");
                if (headPos.x == 0 && headPos.y == 0 && headPos.z == 0) {
                    headPos = target->vOrigin;
                    headPos.z += 60.0f;
                }
                
                SilentAim(cmd, headPos, cgt->refdef.vViewOrigin);
            }
        }
    }
    
    void UpdateTriggerBot() {
        CG_T* cgt = CG_GetCGT(0);
        if (!cgt) return;
        
        // Check if crosshair is on enemy
        centity_t* target = GetCrosshairTarget(cgt);
        if (target && !IsSameTeam(0, target->nextState.iNumber)) {
            // Auto-fire
            if (CL_GetUserCmd) {
                usercmd_s* cmd = CL_GetUserCmd(-1);
                if (cmd) {
                    cmd->iButtons |= BUTTON_ATTACK;
                }
            }
        }
    }
    
    centity_t* GetBestTarget(CG_T* cgt) {
        if (!cgt) return nullptr;
        
        centity_t* bestTarget = nullptr;
        float bestScore = 0.0f;
        Vec3 viewOrigin = cgt->refdef.vViewOrigin;
        
        for (int i = 1; i < 18; i++) {
            centity_t* entity = &cgt->centityArray[i];
            if (!IsValidEntity(entity)) continue;
            
            if (!IsPlayerAlive(i) || IsSameTeam(0, i)) continue;
            
            // Check visibility
            if (!IsEntityVisible(entity, viewOrigin)) continue;
            
            float distance = GetEntityDistance3D(entity, viewOrigin);
            if (distance > 1000.0f) continue; // Too far
            
            // Calculate score based on distance and angle to crosshair
            Vec3 headPos = GetBonePosition((CEntity*)entity, "j_head");
            if (headPos.x == 0 && headPos.y == 0 && headPos.z == 0) {
                headPos = entity->vOrigin;
                headPos.z += 60.0f;
            }
            
            Vec2 screenPos;
            if (WorldToScreenAdvanced(headPos, &screenPos, &cgt->refdef)) {
                float centerX = cgt->refdef.width / 2.0f;
                float centerY = cgt->refdef.height / 2.0f;
                
                float crosshairDistance = sqrt(pow(screenPos.x - centerX, 2) + pow(screenPos.y - centerY, 2));
                
                // Score: closer to crosshair and closer in distance = higher score
                float score = 1000.0f / (distance + 1.0f) + 100.0f / (crosshairDistance + 1.0f);
                
                if (score > bestScore) {
                    bestScore = score;
                    bestTarget = entity;
                }
            }
        }
        
        return bestTarget;
    }
    
    centity_t* GetCrosshairTarget(CG_T* cgt) {
        if (!cgt) return nullptr;
        
        Vec3 viewOrigin = cgt->refdef.vViewOrigin;
        float centerX = cgt->refdef.width / 2.0f;
        float centerY = cgt->refdef.height / 2.0f;
        
        for (int i = 1; i < 18; i++) {
            centity_t* entity = &cgt->centityArray[i];
            if (!IsValidEntity(entity)) continue;
            
            if (!IsPlayerAlive(i)) continue;
            
            Vec3 headPos = GetBonePosition((CEntity*)entity, "j_head");
            if (headPos.x == 0 && headPos.y == 0 && headPos.z == 0) {
                headPos = entity->vOrigin;
                headPos.z += 60.0f;
            }
            
            Vec2 screenPos;
            if (WorldToScreenAdvanced(headPos, &screenPos, &cgt->refdef)) {
                float distance = sqrt(pow(screenPos.x - centerX, 2) + pow(screenPos.y - centerY, 2));
                
                if (distance < 50.0f) { // Within 50 pixels of crosshair
                    return entity;
                }
            }
        }
        
        return nullptr;
    }
    
    void DrawAdvancedInfo() {
        CG_T* cgt = CG_GetCGT(0);
        if (!cgt) return;
        
        float yOffset = 300.0f;
        float lineHeight = 15.0f;
        
        // Display advanced mod status
        char statusText[256];
        
        sprintf_s(statusText, "Advanced Features:");
        DrawText(statusText, 10.0f, yOffset, 0.8f, 0.8f);
        yOffset += lineHeight;
        
        sprintf_s(statusText, "Predictive ESP: %s", predictiveESP ? "ON" : "OFF");
        DrawText(statusText, 10.0f, yOffset, 0.7f, 0.7f);
        yOffset += lineHeight;
        
        sprintf_s(statusText, "Wall Hack ESP: %s", wallHackESP ? "ON" : "OFF");
        DrawText(statusText, 10.0f, yOffset, 0.7f, 0.7f);
        yOffset += lineHeight;
        
        sprintf_s(statusText, "Velocity ESP: %s", velocityESP ? "ON" : "OFF");
        DrawText(statusText, 10.0f, yOffset, 0.7f, 0.7f);
        yOffset += lineHeight;
        
        sprintf_s(statusText, "Radar Hack: %s", radarHack ? "ON" : "OFF");
        DrawText(statusText, 10.0f, yOffset, 0.7f, 0.7f);
        yOffset += lineHeight;
        
        sprintf_s(statusText, "Advanced Aimbot: %s", advancedAimbot ? "ON" : "OFF");
        DrawText(statusText, 10.0f, yOffset, 0.7f, 0.7f);
        yOffset += lineHeight;
        
        sprintf_s(statusText, "Silent Aim: %s", silentAim ? "ON" : "OFF");
        DrawText(statusText, 10.0f, yOffset, 0.7f, 0.7f);
        yOffset += lineHeight;
        
        sprintf_s(statusText, "Trigger Bot: %s", triggerBot ? "ON" : "OFF");
        DrawText(statusText, 10.0f, yOffset, 0.7f, 0.7f);
    }
    
    // Toggle functions
    void TogglePredictiveESP() { predictiveESP = !predictiveESP; }
    void ToggleWallHackESP() { wallHackESP = !wallHackESP; }
    void ToggleVelocityESP() { velocityESP = !velocityESP; }
    void ToggleAdvancedAimbot() { advancedAimbot = !advancedAimbot; }
    void ToggleSilentAim() { silentAim = !silentAim; }
    void ToggleTriggerBot() { triggerBot = !triggerBot; }
    void ToggleRadarHack() { radarHack = !radarHack; }
    void ToggleHealthESP() { healthESP = !healthESP; }
    void ToggleClanTagESP() { clanTagESP = !clanTagESP; }
};

// ========== GLOBAL INSTANCE ==========
AdvancedBO3Mod g_AdvancedMod;

// Main update function
extern "C" __declspec(dllexport) void UpdateAdvancedMods() {
    g_AdvancedMod.Update();
}

// Advanced control functions
extern "C" __declspec(dllexport) void TogglePredictiveESP() { g_AdvancedMod.TogglePredictiveESP(); }
extern "C" __declspec(dllexport) void ToggleWallHackESP() { g_AdvancedMod.ToggleWallHackESP(); }
extern "C" __declspec(dllexport) void ToggleVelocityESP() { g_AdvancedMod.ToggleVelocityESP(); }
extern "C" __declspec(dllexport) void ToggleAdvancedAimbot() { g_AdvancedMod.ToggleAdvancedAimbot(); }
extern "C" __declspec(dllexport) void ToggleSilentAim() { g_AdvancedMod.ToggleSilentAim(); }
extern "C" __declspec(dllexport) void ToggleTriggerBot() { g_AdvancedMod.ToggleTriggerBot(); }
extern "C" __declspec(dllexport) void ToggleRadarHack() { g_AdvancedMod.ToggleRadarHack(); }

// DLL entry point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        // Initialize advanced mod
        break;
    case DLL_PROCESS_DETACH:
        // Cleanup
        break;
    }
    return TRUE;
}
