#include <Windows.h>
#include <TlHelp32.h>
#include <iostream>
#include <string>

class VisibleInjector {
public:
    static DWORD FindProcess(const std::wstring& processName) {
        HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (snapshot == INVALID_HANDLE_VALUE) return 0;
        
        PROCESSENTRY32W pe;
        pe.dwSize = sizeof(pe);
        
        if (Process32FirstW(snapshot, &pe)) {
            do {
                if (processName == pe.szExeFile) {
                    CloseHandle(snapshot);
                    return pe.th32ProcessID;
                }
            } while (Process32NextW(snapshot, &pe));
        }
        
        CloseHandle(snapshot);
        return 0;
    }
    
    static bool InjectDLL(DWORD processId, const std::string& dllPath) {
        std::cout << "Opening process with PID: " << processId << std::endl;
        
        HANDLE process = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
        if (!process) {
            std::cout << "❌ Failed to open process. Error: " << GetLastError() << std::endl;
            std::cout << "Try running as Administrator!" << std::endl;
            return false;
        }
        
        std::cout << "✓ Process opened successfully" << std::endl;
        
        // Allocate memory in target process
        std::cout << "Allocating memory in target process..." << std::endl;
        LPVOID allocatedMemory = VirtualAllocEx(process, nullptr, dllPath.length() + 1,
                                               MEM_RESERVE | MEM_COMMIT, PAGE_READWRITE);
        if (!allocatedMemory) {
            std::cout << "❌ Failed to allocate memory. Error: " << GetLastError() << std::endl;
            CloseHandle(process);
            return false;
        }
        
        std::cout << "✓ Memory allocated at: 0x" << std::hex << allocatedMemory << std::dec << std::endl;
        
        // Write DLL path to allocated memory
        std::cout << "Writing DLL path to target process..." << std::endl;
        if (!WriteProcessMemory(process, allocatedMemory, dllPath.c_str(), dllPath.length() + 1, nullptr)) {
            std::cout << "❌ Failed to write memory. Error: " << GetLastError() << std::endl;
            VirtualFreeEx(process, allocatedMemory, 0, MEM_RELEASE);
            CloseHandle(process);
            return false;
        }
        
        std::cout << "✓ DLL path written successfully" << std::endl;
        
        // Get LoadLibraryA address
        std::cout << "Getting LoadLibraryA address..." << std::endl;
        HMODULE kernel32 = GetModuleHandleA("kernel32.dll");
        FARPROC loadLibraryAddr = GetProcAddress(kernel32, "LoadLibraryA");
        
        if (!loadLibraryAddr) {
            std::cout << "❌ Failed to get LoadLibraryA address" << std::endl;
            VirtualFreeEx(process, allocatedMemory, 0, MEM_RELEASE);
            CloseHandle(process);
            return false;
        }
        
        std::cout << "✓ LoadLibraryA address: 0x" << std::hex << loadLibraryAddr << std::dec << std::endl;
        
        // Create remote thread
        std::cout << "Creating remote thread..." << std::endl;
        HANDLE remoteThread = CreateRemoteThread(process, nullptr, 0,
                                                (LPTHREAD_START_ROUTINE)loadLibraryAddr,
                                                allocatedMemory, 0, nullptr);
        if (!remoteThread) {
            std::cout << "❌ Failed to create remote thread. Error: " << GetLastError() << std::endl;
            VirtualFreeEx(process, allocatedMemory, 0, MEM_RELEASE);
            CloseHandle(process);
            return false;
        }
        
        std::cout << "✓ Remote thread created successfully" << std::endl;
        std::cout << "Waiting for injection to complete..." << std::endl;
        
        // Wait for injection to complete
        DWORD waitResult = WaitForSingleObject(remoteThread, 5000); // 5 second timeout
        
        if (waitResult == WAIT_TIMEOUT) {
            std::cout << "⚠️ Injection timed out, but may still be loading..." << std::endl;
        } else if (waitResult == WAIT_OBJECT_0) {
            std::cout << "✓ Remote thread completed" << std::endl;
        }
        
        // Get thread exit code
        DWORD exitCode;
        if (GetExitCodeThread(remoteThread, &exitCode)) {
            if (exitCode != 0) {
                std::cout << "✓ DLL loaded successfully (Handle: 0x" << std::hex << exitCode << std::dec << ")" << std::endl;
            } else {
                std::cout << "❌ DLL failed to load (Exit code: 0)" << std::endl;
            }
        }
        
        // Cleanup
        CloseHandle(remoteThread);
        VirtualFreeEx(process, allocatedMemory, 0, MEM_RELEASE);
        CloseHandle(process);
        
        return true;
    }
};

int main(int argc, char* argv[]) {
    std::cout << "========================================\n";
    std::cout << "BO3 VISIBLE MOD INJECTOR\n";
    std::cout << "========================================\n";
    std::cout << "This injector creates visible interfaces!\n";
    std::cout << "WARNING: Educational use only!\n";
    std::cout << "Never use in online multiplayer!\n";
    std::cout << "========================================\n\n";
    
    std::string dllPath = "VisibleMod.dll";
    if (argc > 1) {
        dllPath = argv[1];
    }
    
    // Convert to full path
    char fullPath[MAX_PATH];
    if (GetFullPathNameA(dllPath.c_str(), MAX_PATH, fullPath, nullptr) == 0) {
        std::cout << "❌ Failed to get full path for DLL" << std::endl;
        std::cout << "Press Enter to exit...";
        std::cin.get();
        return 1;
    }
    
    dllPath = fullPath;
    std::cout << "DLL Path: " << dllPath << std::endl;
    
    // Check if DLL exists
    if (GetFileAttributesA(dllPath.c_str()) == INVALID_FILE_ATTRIBUTES) {
        std::cout << "❌ DLL file not found: " << dllPath << std::endl;
        std::cout << "Make sure VisibleMod.dll is in the same directory!" << std::endl;
        std::cout << "Press Enter to exit...";
        std::cin.get();
        return 1;
    }
    
    std::cout << "✓ DLL file found" << std::endl;
    
    // Find BlackOps3.exe
    std::cout << "\nLooking for BlackOps3.exe..." << std::endl;
    DWORD processId = VisibleInjector::FindProcess(L"BlackOps3.exe");
    
    if (processId == 0) {
        std::cout << "❌ BlackOps3.exe not found!" << std::endl;
        std::cout << "Make sure the game is running." << std::endl;
        std::cout << "Press Enter to exit...";
        std::cin.get();
        return 1;
    }
    
    std::cout << "✓ Found BlackOps3.exe (PID: " << processId << ")" << std::endl;
    std::cout << "\nStarting injection process..." << std::endl;
    std::cout << "========================================" << std::endl;
    
    if (VisibleInjector::InjectDLL(processId, dllPath)) {
        std::cout << "========================================" << std::endl;
        std::cout << "🎉 INJECTION COMPLETED!" << std::endl;
        std::cout << "========================================" << std::endl;
        std::cout << "\nWhat should happen now:" << std::endl;
        std::cout << "1. A MESSAGE BOX should appear" << std::endl;
        std::cout << "2. A CONSOLE WINDOW should open" << std::endl;
        std::cout << "3. A STATUS WINDOW should appear" << std::endl;
        std::cout << "\nIf you don't see these, the injection may have failed." << std::endl;
        std::cout << "\nControls in the game:" << std::endl;
        std::cout << "F1 - Toggle ESP" << std::endl;
        std::cout << "F2 - Toggle Aimbot" << std::endl;
        std::cout << "F3 - Toggle Info Display" << std::endl;
        std::cout << "ESC - Exit mod" << std::endl;
        std::cout << "\nA log file 'BO3_Mod_Log.txt' will be created." << std::endl;
        std::cout << "\nPress Enter to exit injector...";
        std::cin.get();
    } else {
        std::cout << "========================================" << std::endl;
        std::cout << "❌ INJECTION FAILED!" << std::endl;
        std::cout << "========================================" << std::endl;
        std::cout << "Try running as Administrator." << std::endl;
        std::cout << "Press Enter to exit...";
        std::cin.get();
        return 1;
    }
    
    return 0;
}
