#!/usr/bin/env python3
"""
BO3 Mod Framework Validation Script
Validates all framework components and generates a comprehensive report
"""

import os
import sys
import re
import json
from pathlib import Path
from datetime import datetime

class BO3FrameworkValidator:
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'framework_version': '1.0.0',
            'game_version': '********',
            'validation_results': {},
            'issues': [],
            'recommendations': []
        }
        
    def validate_file_structure(self):
        """Validate that all required files are present"""
        required_files = [
            'BO3_ModFramework.h',
            'BO3_ModFramework.cpp', 
            'ExampleMod.cpp',
            'Injector.cpp',
            'CMakeLists.txt',
            'Makefile',
            'build.bat',
            'test_build.cpp',
            'README.md',
            'TECHNICAL_DETAILS.md'
        ]
        
        missing_files = []
        present_files = []
        
        for file in required_files:
            if os.path.exists(f"../BLOPS3/{file}"):
                present_files.append(file)
            else:
                missing_files.append(file)
        
        self.results['validation_results']['file_structure'] = {
            'status': 'PASS' if not missing_files else 'FAIL',
            'present_files': present_files,
            'missing_files': missing_files,
            'total_files': len(required_files),
            'present_count': len(present_files)
        }
        
        if missing_files:
            self.results['issues'].append(f"Missing required files: {', '.join(missing_files)}")
    
    def validate_header_file(self):
        """Validate the main header file structure"""
        header_path = "../BLOPS3/BO3_ModFramework.h"
        if not os.path.exists(header_path):
            self.results['issues'].append("Header file not found")
            return
            
        with open(header_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Check for essential components
        checks = {
            'include_guards': '#pragma once' in content or '#ifndef' in content,
            'windows_includes': '#include <Windows.h>' in content,
            'directx_includes': '#include <d3d9.h>' in content,
            'vec3_struct': 'typedef struct' in content and 'Vec3' in content,
            'entity_class': 'class CEntity' in content,
            'client_info_class': 'class CClientInfo' in content,
            'refdef_class': 'class RefDef' in content,
            'function_signatures': 'typedef' in content and 'CG_GetEntity_' in content,
            'memory_addresses': 'extern' in content,
            'entity_flags': 'enum EFlags' in content or 'EF_STANDING' in content
        }
        
        passed_checks = sum(checks.values())
        total_checks = len(checks)
        
        self.results['validation_results']['header_file'] = {
            'status': 'PASS' if passed_checks == total_checks else 'PARTIAL',
            'checks_passed': passed_checks,
            'total_checks': total_checks,
            'detailed_checks': checks
        }
        
        if passed_checks < total_checks:
            failed_checks = [k for k, v in checks.items() if not v]
            self.results['issues'].append(f"Header file missing components: {', '.join(failed_checks)}")
    
    def validate_implementation_file(self):
        """Validate the implementation file"""
        impl_path = "../BLOPS3/BO3_ModFramework.cpp"
        if not os.path.exists(impl_path):
            self.results['issues'].append("Implementation file not found")
            return
            
        with open(impl_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Check for memory addresses from UnknownCheats thread
        memory_addresses = [
            '0x140032530',  # CG_GetEntity
            '0x140032660',  # CG_GetCGT
            '0x1400858E0',  # CG_GetCGS
            '0x140405290',  # DrawEngineText
            '0x1400C9C70',  # World2Screen
            '0x14031AC90',  # RegisterTag
            '0x1400FBDE0'   # GetTagPos
        ]
        
        address_checks = {addr: addr in content for addr in memory_addresses}
        
        # Check for utility functions
        utility_functions = [
            'GetClientByIndex',
            'DrawText',
            'WorldToScreen',
            'GetBonePosition',
            'IsEnemy',
            'IsValidEntity',
            'GetDistance'
        ]
        
        function_checks = {func: func in content for func in utility_functions}
        
        self.results['validation_results']['implementation_file'] = {
            'status': 'PASS' if all(address_checks.values()) and all(function_checks.values()) else 'PARTIAL',
            'memory_addresses': address_checks,
            'utility_functions': function_checks,
            'addresses_found': sum(address_checks.values()),
            'functions_found': sum(function_checks.values())
        }
    
    def validate_example_mod(self):
        """Validate the example mod implementation"""
        mod_path = "../BLOPS3/ExampleMod.cpp"
        if not os.path.exists(mod_path):
            self.results['issues'].append("Example mod file not found")
            return
            
        with open(mod_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Check for mod features
        features = {
            'esp_system': 'DrawESP' in content,
            'info_display': 'DrawGameInformation' in content,
            'aimbot_framework': 'SimpleAimbot' in content,
            'hotkey_support': 'ToggleESP' in content,
            'dll_exports': '__declspec(dllexport)' in content,
            'main_update': 'UpdateMods' in content,
            'player_validation': 'IsValidEntity' in content,
            'team_detection': 'IsEnemy' in content
        }
        
        self.results['validation_results']['example_mod'] = {
            'status': 'PASS' if all(features.values()) else 'PARTIAL',
            'features': features,
            'features_implemented': sum(features.values()),
            'total_features': len(features)
        }
    
    def validate_injector(self):
        """Validate the DLL injector"""
        injector_path = "../BLOPS3/Injector.cpp"
        if not os.path.exists(injector_path):
            self.results['issues'].append("Injector file not found")
            return
            
        with open(injector_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Check for injector features
        features = {
            'process_enumeration': 'CreateToolhelp32Snapshot' in content,
            'dll_injection': 'VirtualAllocEx' in content and 'WriteProcessMemory' in content,
            'remote_thread': 'CreateRemoteThread' in content,
            'hotkey_management': 'GetAsyncKeyState' in content,
            'error_handling': 'GetLastError' in content,
            'target_process': 'BlackOps3.exe' in content
        }
        
        self.results['validation_results']['injector'] = {
            'status': 'PASS' if all(features.values()) else 'PARTIAL',
            'features': features,
            'features_implemented': sum(features.values()),
            'total_features': len(features)
        }
    
    def validate_build_system(self):
        """Validate build configuration files"""
        build_files = {
            'cmake': '../BLOPS3/CMakeLists.txt',
            'makefile': '../BLOPS3/Makefile',
            'batch_script': '../BLOPS3/build.bat'
        }
        
        build_status = {}
        
        for name, path in build_files.items():
            if os.path.exists(path):
                with open(path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                if name == 'cmake':
                    build_status[name] = {
                        'exists': True,
                        'has_targets': 'add_executable' in content and 'add_library' in content,
                        'has_dependencies': 'd3d9' in content,
                        'has_install': 'install(' in content
                    }
                elif name == 'makefile':
                    build_status[name] = {
                        'exists': True,
                        'has_targets': 'all:' in content,
                        'has_dependencies': '-ld3d9' in content,
                        'has_clean': 'clean:' in content
                    }
                elif name == 'batch_script':
                    build_status[name] = {
                        'exists': True,
                        'has_cmake': 'cmake' in content,
                        'has_build': '--build' in content,
                        'has_warnings': 'WARNING' in content
                    }
            else:
                build_status[name] = {'exists': False}
        
        self.results['validation_results']['build_system'] = build_status
    
    def validate_documentation(self):
        """Validate documentation completeness"""
        doc_files = {
            'readme': '../BLOPS3/README.md',
            'technical': '../BLOPS3/TECHNICAL_DETAILS.md'
        }
        
        doc_status = {}
        
        for name, path in doc_files.items():
            if os.path.exists(path):
                with open(path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                doc_status[name] = {
                    'exists': True,
                    'has_warnings': 'WARNING' in content or 'DISCLAIMER' in content,
                    'has_usage': 'Usage' in content or 'How to' in content,
                    'has_examples': 'Example' in content or 'example' in content,
                    'word_count': len(content.split())
                }
            else:
                doc_status[name] = {'exists': False}
        
        self.results['validation_results']['documentation'] = doc_status
    
    def generate_recommendations(self):
        """Generate recommendations based on validation results"""
        recommendations = []
        
        # Check overall framework completeness
        total_checks = 0
        passed_checks = 0
        
        for category, results in self.results['validation_results'].items():
            if isinstance(results, dict) and 'status' in results:
                total_checks += 1
                if results['status'] == 'PASS':
                    passed_checks += 1
        
        completion_rate = (passed_checks / total_checks * 100) if total_checks > 0 else 0
        
        if completion_rate >= 90:
            recommendations.append("✅ Framework is highly complete and ready for use")
        elif completion_rate >= 70:
            recommendations.append("⚠️ Framework is mostly complete but has some issues to address")
        else:
            recommendations.append("❌ Framework needs significant work before use")
        
        # Specific recommendations
        if not self.results['validation_results'].get('file_structure', {}).get('status') == 'PASS':
            recommendations.append("📁 Ensure all required files are present")
        
        if not self.results['validation_results'].get('header_file', {}).get('status') == 'PASS':
            recommendations.append("📋 Review header file for missing components")
        
        recommendations.extend([
            "🔒 Only use this framework in offline/educational environments",
            "⚠️ Verify game version (********) before use",
            "🛠️ Test build system before attempting compilation",
            "📖 Read all documentation and warnings carefully",
            "🎯 Start with the example mod to understand the framework"
        ])
        
        self.results['recommendations'] = recommendations
        self.results['completion_rate'] = completion_rate
    
    def run_validation(self):
        """Run complete validation suite"""
        print("🔍 Starting BO3 Mod Framework Validation...")
        
        self.validate_file_structure()
        print("✓ File structure validation complete")
        
        self.validate_header_file()
        print("✓ Header file validation complete")
        
        self.validate_implementation_file()
        print("✓ Implementation file validation complete")
        
        self.validate_example_mod()
        print("✓ Example mod validation complete")
        
        self.validate_injector()
        print("✓ Injector validation complete")
        
        self.validate_build_system()
        print("✓ Build system validation complete")
        
        self.validate_documentation()
        print("✓ Documentation validation complete")
        
        self.generate_recommendations()
        print("✓ Recommendations generated")
        
        return self.results
    
    def save_report(self, filename="framework_validation_report.json"):
        """Save validation report to file"""
        with open(f"../BLOPS3/{filename}", 'w') as f:
            json.dump(self.results, f, indent=2)
        print(f"📄 Validation report saved to {filename}")

def main():
    print("=" * 60)
    print("Call of Duty: Black Ops 3 Mod Framework Validator")
    print("=" * 60)
    
    validator = BO3FrameworkValidator()
    results = validator.run_validation()
    
    print("\n" + "=" * 60)
    print("VALIDATION SUMMARY")
    print("=" * 60)
    
    print(f"Framework Version: {results['framework_version']}")
    print(f"Target Game Version: {results['game_version']}")
    print(f"Completion Rate: {results['completion_rate']:.1f}%")
    
    if results['issues']:
        print(f"\n❌ Issues Found ({len(results['issues'])}):")
        for issue in results['issues']:
            print(f"  • {issue}")
    
    print(f"\n💡 Recommendations:")
    for rec in results['recommendations']:
        print(f"  {rec}")
    
    validator.save_report()
    
    print("\n" + "=" * 60)
    print("VALIDATION COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main()
