#!/usr/bin/env python3
"""
BO3 Mod Framework Package Creator
Creates a complete distribution package with all necessary files
"""

import os
import shutil
import json
import zipfile
from datetime import datetime
from pathlib import Path

class BO3PackageCreator:
    def __init__(self):
        self.package_name = f"BO3_ModFramework_v1.0.0_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.package_dir = f"../BLOPS3/{self.package_name}"
        self.source_dir = "../BLOPS3"
        
    def create_directory_structure(self):
        """Create the package directory structure"""
        directories = [
            self.package_dir,
            f"{self.package_dir}/src",
            f"{self.package_dir}/docs",
            f"{self.package_dir}/examples",
            f"{self.package_dir}/build_tools",
            f"{self.package_dir}/validation"
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            print(f"✓ Created directory: {directory}")
    
    def copy_source_files(self):
        """Copy all source files to the package"""
        source_files = [
            ("BO3_ModFramework.h", "src/"),
            ("BO3_ModFramework.cpp", "src/"),
            ("ExampleMod.cpp", "examples/"),
            ("Injector.cpp", "src/"),
            ("test_build.cpp", "examples/")
        ]
        
        for src_file, dest_dir in source_files:
            src_path = f"{self.source_dir}/{src_file}"
            dest_path = f"{self.package_dir}/{dest_dir}{src_file}"
            
            if os.path.exists(src_path):
                shutil.copy2(src_path, dest_path)
                print(f"✓ Copied: {src_file}")
            else:
                print(f"⚠️ Missing: {src_file}")
    
    def copy_build_files(self):
        """Copy build system files"""
        build_files = [
            ("CMakeLists.txt", "build_tools/"),
            ("Makefile", "build_tools/"),
            ("build.bat", "build_tools/")
        ]
        
        for src_file, dest_dir in build_files:
            src_path = f"{self.source_dir}/{src_file}"
            dest_path = f"{self.package_dir}/{dest_dir}{src_file}"
            
            if os.path.exists(src_path):
                shutil.copy2(src_path, dest_path)
                print(f"✓ Copied build file: {src_file}")
    
    def copy_documentation(self):
        """Copy all documentation files"""
        doc_files = [
            ("README.md", "docs/"),
            ("TECHNICAL_DETAILS.md", "docs/"),
            ("USAGE_GUIDE.md", "docs/")
        ]
        
        for src_file, dest_dir in doc_files:
            src_path = f"{self.source_dir}/{src_file}"
            if not os.path.exists(src_path):
                src_path = f"build/{src_file}"  # Check build directory
            
            dest_path = f"{self.package_dir}/{dest_dir}{src_file}"
            
            if os.path.exists(src_path):
                shutil.copy2(src_path, dest_path)
                print(f"✓ Copied documentation: {src_file}")
    
    def copy_validation_tools(self):
        """Copy validation and testing tools"""
        validation_files = [
            ("validate_framework.py", "validation/"),
            ("framework_validation_report.json", "validation/")
        ]
        
        for src_file, dest_dir in validation_files:
            src_path = f"build/{src_file}"
            if not os.path.exists(src_path):
                src_path = f"{self.source_dir}/{src_file}"
            
            dest_path = f"{self.package_dir}/{dest_dir}{src_file}"
            
            if os.path.exists(src_path):
                shutil.copy2(src_path, dest_path)
                print(f"✓ Copied validation tool: {src_file}")
    
    def create_package_info(self):
        """Create package information file"""
        package_info = {
            "name": "Call of Duty: Black Ops 3 Mod Framework",
            "version": "1.0.0",
            "created": datetime.now().isoformat(),
            "target_game": "Call of Duty: Black Ops 3",
            "game_version": "37.1.1.0",
            "architecture": "x64",
            "purpose": "Educational reverse engineering and modding framework",
            "warning": "FOR EDUCATIONAL USE ONLY - DO NOT USE ONLINE",
            "components": {
                "framework_library": "Core modding framework with game structures",
                "example_mod": "Complete example mod with ESP and aimbot",
                "injector": "DLL injection tool with hotkey support",
                "build_system": "CMake and Makefile build configurations",
                "documentation": "Comprehensive guides and technical details",
                "validation": "Framework validation and testing tools"
            },
            "requirements": {
                "compiler": "Visual Studio 2019+ or MinGW-w64",
                "cmake": "3.16 or later (optional)",
                "directx_sdk": "For D3D9 headers",
                "windows_sdk": "For Windows API headers"
            },
            "files": {
                "src/": "Source code files",
                "docs/": "Documentation and guides",
                "examples/": "Example implementations",
                "build_tools/": "Build system files",
                "validation/": "Testing and validation tools"
            }
        }
        
        info_path = f"{self.package_dir}/package_info.json"
        with open(info_path, 'w', encoding='utf-8') as f:
            json.dump(package_info, f, indent=2)
        
        print(f"✓ Created package info: package_info.json")
    
    def create_quick_start(self):
        """Create a quick start guide"""
        quick_start = """# BO3 Mod Framework - Quick Start

## ⚠️ IMPORTANT WARNINGS
- **EDUCATIONAL PURPOSE ONLY**
- **NEVER USE ONLINE** - You will be banned
- **OFFLINE TESTING ONLY**

## Quick Setup

1. **Verify Game Version**: Black Ops 3 version 37.1.1.0
2. **Build Framework**:
   ```
   cd build_tools
   build.bat
   ```
3. **Validate Setup**:
   ```
   cd validation
   python validate_framework.py
   ```
4. **Test Framework**:
   ```
   cd examples
   # Compile and test example mod
   ```

## File Structure
- `src/` - Core framework source code
- `docs/` - Complete documentation
- `examples/` - Example mod implementations
- `build_tools/` - Build system files
- `validation/` - Testing tools

## Next Steps
1. Read `docs/README.md` for complete overview
2. Study `docs/TECHNICAL_DETAILS.md` for technical info
3. Follow `docs/USAGE_GUIDE.md` for detailed instructions
4. Start with `examples/ExampleMod.cpp` to understand the framework

## Support
- Validate your setup regularly
- Read all documentation carefully
- Use only in educational/offline environments
- Respect game terms of service

**Remember: This is a learning tool for understanding game modding concepts!**
"""
        
        quick_start_path = f"{self.package_dir}/QUICK_START.md"
        with open(quick_start_path, 'w', encoding='utf-8') as f:
            f.write(quick_start)
        
        print(f"✓ Created quick start guide")
    
    def create_license(self):
        """Create license file"""
        license_text = """MIT License

Copyright (c) 2025 BO3 Mod Framework

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

EDUCATIONAL USE DISCLAIMER:
This software is provided for educational purposes only. The use of this
software for cheating in online games, violating terms of service, or any
illegal activities is strictly prohibited. Users are solely responsible for
ensuring their use complies with applicable laws and terms of service.

ANTI-CHEAT WARNING:
Using this software in online multiplayer games may result in permanent
account bans. The authors are not responsible for any consequences resulting
from the use of this software.
"""
        
        license_path = f"{self.package_dir}/LICENSE"
        with open(license_path, 'w', encoding='utf-8') as f:
            f.write(license_text)
        
        print(f"✓ Created license file")
    
    def create_zip_archive(self):
        """Create a ZIP archive of the package"""
        zip_path = f"{self.package_dir}.zip"
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(self.package_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_path = os.path.relpath(file_path, os.path.dirname(self.package_dir))
                    zipf.write(file_path, arc_path)
        
        print(f"✓ Created ZIP archive: {os.path.basename(zip_path)}")
        return zip_path
    
    def generate_checksums(self):
        """Generate checksums for important files"""
        import hashlib
        
        checksums = {}
        important_files = [
            "src/BO3_ModFramework.h",
            "src/BO3_ModFramework.cpp",
            "examples/ExampleMod.cpp",
            "src/Injector.cpp"
        ]
        
        for file_path in important_files:
            full_path = f"{self.package_dir}/{file_path}"
            if os.path.exists(full_path):
                with open(full_path, 'rb') as f:
                    content = f.read()
                    checksums[file_path] = {
                        'md5': hashlib.md5(content).hexdigest(),
                        'sha256': hashlib.sha256(content).hexdigest()
                    }
        
        checksum_path = f"{self.package_dir}/checksums.json"
        with open(checksum_path, 'w', encoding='utf-8') as f:
            json.dump(checksums, f, indent=2)
        
        print(f"✓ Generated checksums")
    
    def create_package(self):
        """Create the complete package"""
        print("=" * 60)
        print("Creating BO3 Mod Framework Distribution Package")
        print("=" * 60)
        
        self.create_directory_structure()
        self.copy_source_files()
        self.copy_build_files()
        self.copy_documentation()
        self.copy_validation_tools()
        self.create_package_info()
        self.create_quick_start()
        self.create_license()
        self.generate_checksums()
        
        zip_path = self.create_zip_archive()
        
        print("\n" + "=" * 60)
        print("PACKAGE CREATION COMPLETE")
        print("=" * 60)
        print(f"Package Directory: {self.package_dir}")
        print(f"ZIP Archive: {zip_path}")
        print(f"Package Size: {self.get_directory_size(self.package_dir):.2f} MB")
        
        return self.package_dir, zip_path
    
    def get_directory_size(self, directory):
        """Calculate directory size in MB"""
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(directory):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                total_size += os.path.getsize(filepath)
        return total_size / (1024 * 1024)

def main():
    creator = BO3PackageCreator()
    package_dir, zip_path = creator.create_package()
    
    print("\n📦 Package Contents:")
    for root, dirs, files in os.walk(package_dir):
        level = root.replace(package_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            print(f"{subindent}{file}")
    
    print(f"\n✅ Distribution package ready!")
    print(f"📁 Directory: {package_dir}")
    print(f"📦 Archive: {zip_path}")

if __name__ == "__main__":
    main()
