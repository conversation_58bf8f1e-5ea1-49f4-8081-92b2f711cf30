#include "BO3_ModFramework.h"
#include <math.h>
#include <cstring>
#include <cstdio>

// ========== MEMORY ADDRESSES (Version ********) ==========
// These addresses are from the UnknownCheats forum thread

CG_GetEntity_ CG_GetEntity = (CG_GetEntity_)0x140032530;
CG_GetCGT_ CG_GetCGT = (CG_GetCGT_)0x140032660;
CG_GetCGS_ CG_GetCGS = (CG_GetCGS_)0x1400858E0;
CG_GetPredictedPlayerState_ CG_GetPredictedPlayerState = (CG_GetPredictedPlayerState_)0x1400F5C90;

DrawEngineText_ DrawEngineText = (DrawEngineText_)0x140405290;
GetFont_ GetFont = (GetFont_)0x1403DCD90;
DrawNameTags_ DrawNameTags = (DrawNameTags_)0x1400CC0F0;

RegisterShader_ RegisterShader = (RegisterShader_)0x140400F20;
World2Screen_ World2Screen = (World2Screen_)0x1400C9C70;

RegisterTag_ RegisterTag = (RegisterTag_)0x14031AC90;
GetTagPos_ GetTagPos = (GetTagPos_)0x1400FBDE0;

CG_GetWeaponInfo_ CG_GetWeaponInfo = (CG_GetWeaponInfo_)0x1407E1780;

// Drawing function addresses
DWORD DrawStretchPicAddr = 0x14081D1A0;
DWORD DrawRotatedStretchPicAddr = 0x14081D290;

// Radar overlay functions
DWORD DrawRadarOverlayAddr = 0x1405ADAE0;
DWORD DrawFriendlyIconsAddr = 0x1405B05F0;
DWORD DrawEnemyIconsAddr = 0x1405AEE30;

// ========== UTILITY FUNCTION IMPLEMENTATIONS ==========

CClientInfo* GetClientByIndex(int idx) {
    CG_T* cgt = CG_GetCGT(0);
    if (!cgt) return nullptr;
    
    // Calculate client info offset based on forum thread info
    return (CClientInfo*)((DWORD_PTR)cgt + 0x2E6BB4 + (idx * 0xEB0) - 0x2C);
}

void DrawText(char* szText, float x, float y, float scalex, float scaley) {
    if (!szText || !DrawEngineText || !GetFont) return;
    
    // White color array
    static float whiteColor[4] = { 1.0f, 1.0f, 1.0f, 1.0f };
    
    DrawEngineText(szText, 0x7FFFFFFF, GetFont(), x, y, scalex, scaley, 1.0f, whiteColor, 0);
}

shader_t* GetShader(char* szShader) {
    if (!RegisterShader || !szShader) return nullptr;
    return RegisterShader(szShader, 7, 1, 0x0FFFFFFFF);
}

bool WorldToScreen(Vec3 worldPos, Vec2& screenPos) {
    if (!World2Screen) return false;
    
    float x, y;
    int result = World2Screen(0, worldPos, &x, &y);
    
    if (result) {
        screenPos.x = x;
        screenPos.y = y;
        return true;
    }
    return false;
}

Vec3 GetBonePosition(CEntity* entity, const char* boneName) {
    Vec3 bonePos = { 0, 0, 0 };
    
    if (!entity || !boneName || !RegisterTag || !GetTagPos) 
        return bonePos;
    
    WORD boneTag = RegisterTag((char*)boneName);
    if (boneTag) {
        GetTagPos(entity, boneTag, &bonePos);
    }
    
    return bonePos;
}

bool IsEnemy(CClientInfo* client, int localTeam) {
    if (!client) return false;
    return client->iTeam != localTeam && client->iTeam != 0;
}

bool IsValidEntity(CEntity* entity) {
    if (!entity) return false;
    
    // Check if entity is alive and valid type
    return entity->IsAlive && (entity->Type == 1); // Type 1 = player
}

float GetDistance(Vec3 pos1, Vec3 pos2) {
    float dx = pos1.x - pos2.x;
    float dy = pos1.y - pos2.y;
    float dz = pos1.z - pos2.z;
    return sqrt(dx*dx + dy*dy + dz*dz);
}

// ========== ADVANCED MODDING FUNCTIONS ==========

// ESP Box drawing
void DrawESPBox(CEntity* entity, float* color) {
    if (!entity || !IsValidEntity(entity)) return;
    
    Vec3 headPos = GetBonePosition(entity, "j_head");
    Vec3 feetPos = entity->vOrigin;
    
    Vec2 headScreen, feetScreen;
    if (WorldToScreen(headPos, headScreen) && WorldToScreen(feetPos, feetScreen)) {
        float height = feetScreen.y - headScreen.y;
        float width = height * 0.4f; // Proportional width
        
        // Draw box outline (would need actual drawing implementation)
        // This is a placeholder - you'd implement actual line drawing here
    }
}

// Skeleton ESP
void DrawSkeleton(CEntity* entity, float* color) {
    if (!entity || !IsValidEntity(entity)) return;
    
    // Define bone connections for skeleton
    const char* boneConnections[][2] = {
        {"j_neck", "j_shoulder_le"},
        {"j_neck", "j_shoulder_ri"},
        {"j_shoulder_le", "j_elbow_le"},
        {"j_shoulder_ri", "j_elbow_ri"},
        {"j_elbow_le", "j_wrist_le"},
        {"j_elbow_ri", "j_wrist_ri"},
        {"j_neck", "j_spinelower"},
        {"j_spinelower", "j_knee_le"},
        {"j_spinelower", "j_knee_ri"},
        {"j_knee_le", "j_ankle_le"},
        {"j_knee_ri", "j_ankle_ri"}
    };
    
    int numConnections = sizeof(boneConnections) / sizeof(boneConnections[0]);
    
    for (int i = 0; i < numConnections; i++) {
        Vec3 bone1Pos = GetBonePosition(entity, boneConnections[i][0]);
        Vec3 bone2Pos = GetBonePosition(entity, boneConnections[i][1]);
        
        Vec2 screen1, screen2;
        if (WorldToScreen(bone1Pos, screen1) && WorldToScreen(bone2Pos, screen2)) {
            // Draw line between bones (placeholder - implement actual line drawing)
        }
    }
}

// Aimbot helper - get closest enemy to crosshair
CEntity* GetClosestEnemyToCrosshair(float maxDistance, float maxFOV) {
    CG_T* cgt = CG_GetCGT(0);
    if (!cgt) return nullptr;
    
    RefDef* refdef = (RefDef*)((DWORD_PTR)cgt + 0x130FB4);
    if (!refdef) return nullptr;
    
    CEntity* closestEntity = nullptr;
    float closestDistance = maxDistance;
    
    // Screen center
    float centerX = refdef->width / 2.0f;
    float centerY = refdef->height / 2.0f;
    
    // Get local team
    CClientInfo* localClient = GetClientByIndex(0);
    int localTeam = localClient ? localClient->iTeam : 0;
    
    // Loop through all entities
    for (int i = 1; i < 64; i++) { // Skip local player (index 0)
        CEntity* entity = CG_GetEntity(0, i);
        if (!IsValidEntity(entity)) continue;
        
        CClientInfo* client = GetClientByIndex(i);
        if (!IsEnemy(client, localTeam)) continue;
        
        // Get head position for aiming
        Vec3 headPos = GetBonePosition(entity, "j_head");
        Vec2 screenPos;
        
        if (WorldToScreen(headPos, screenPos)) {
            // Calculate distance from crosshair
            float dx = screenPos.x - centerX;
            float dy = screenPos.y - centerY;
            float screenDistance = sqrt(dx*dx + dy*dy);
            
            // Check FOV constraint
            if (screenDistance > maxFOV) continue;
            
            // Calculate 3D distance
            float worldDistance = GetDistance(refdef->ViewOrigin, headPos);
            
            if (worldDistance < closestDistance) {
                closestDistance = worldDistance;
                closestEntity = entity;
            }
        }
    }
    
    return closestEntity;
}

// Weapon information helper
const char* GetCurrentWeaponName() {
    CClientInfo* localClient = GetClientByIndex(0);
    if (!localClient) return "Unknown";
    
    CG_WeaponInfo* weaponInfo = CG_GetWeaponInfo(localClient->WpnIndex);
    if (weaponInfo && weaponInfo->cleanName) {
        return weaponInfo->cleanName;
    }
    
    return "Unknown";
}

// Check if player is in game
bool IsInGame() {
    playerState_s* ps = CG_GetPredictedPlayerState(0);
    return ps && ps->otherFlags != 0;
}

// Get game information
void GetGameInfo(char* gameType, char* mapName, char* hostName) {
    cgs_t* cgs = CG_GetCGS(0);
    if (!cgs) return;
    
    if (gameType) strcpy_s(gameType, 32, cgs->gametype);
    if (mapName) strcpy_s(mapName, 32, cgs->mapName);
    if (hostName) strcpy_s(hostName, 256, cgs->hostname);
}
