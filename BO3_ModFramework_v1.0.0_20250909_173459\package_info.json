{"name": "Call of Duty: Black Ops 3 Mod Framework", "version": "1.0.0", "created": "2025-09-09T17:34:59.074861", "target_game": "Call of Duty: Black Ops 3", "game_version": "37.1.1.0", "architecture": "x64", "purpose": "Educational reverse engineering and modding framework", "warning": "FOR EDUCATIONAL USE ONLY - DO NOT USE ONLINE", "components": {"framework_library": "Core modding framework with game structures", "example_mod": "Complete example mod with ESP and aimbot", "injector": "DLL injection tool with hotkey support", "build_system": "CMake and Makefile build configurations", "documentation": "Comprehensive guides and technical details", "validation": "Framework validation and testing tools"}, "requirements": {"compiler": "Visual Studio 2019+ or MinGW-w64", "cmake": "3.16 or later (optional)", "directx_sdk": "For D3D9 headers", "windows_sdk": "For Windows API headers"}, "files": {"src/": "Source code files", "docs/": "Documentation and guides", "examples/": "Example implementations", "build_tools/": "Build system files", "validation/": "Testing and validation tools"}}