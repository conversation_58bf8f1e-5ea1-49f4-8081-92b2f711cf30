# Call of Duty: Black Ops 3 Modding Framework

This framework provides a comprehensive foundation for creating mods for Call of Duty: Black Ops 3, based on reverse engineering information from the UnknownCheats community.

## ⚠️ Important Disclaimer

**This framework is for educational purposes only.** Using mods in online multiplayer games may result in:
- Account bans
- VAC (Valve Anti-Cheat) bans
- Violation of game terms of service

**Use at your own risk and only in offline/private environments.**

## 📋 Features

### Core Framework (`BO3_ModFramework.h/cpp`)
- Complete game structure definitions
- Memory address mappings for version ********
- Entity management and validation
- Player information access
- Weapon data retrieval
- World-to-screen conversion
- Bone/tag position calculation
- Team detection utilities

### Example Mod (`ExampleMod.cpp`)
- **ESP (Extra Sensory Perception)**
  - Player name display
  - Distance calculation
  - Skeleton ESP
  - Box ESP
  - Team-based coloring
- **Information Display**
  - Game type and map name
  - Current weapon information
  - Player count
- **Basic Aimbot Framework**
  - Target acquisition
  - Angle calculation
  - FOV and distance constraints

## 🏗️ Game Structures

### Key Structures Implemented

1. **CEntity** - Represents game entities (players, objects)
   - Position and rotation data
   - Entity flags (alive, stance, etc.)
   - Entity type identification

2. **CClientInfo** - Player-specific information
   - Player name
   - Team assignment
   - Weapon information

3. **RefDef** - Rendering/camera information
   - Screen dimensions
   - View origin and angles
   - FOV data

4. **CG_T** - Main game state structure
   - Local player index
   - Contains RefDef and other game data

## 🔧 Memory Addresses (Version ********)

The framework includes memory addresses for:
- `CG_GetEntity`: 0x140032530
- `CG_GetCGT`: 0x140032660
- `DrawEngineText`: 0x140405290
- `World2Screen`: 0x1400C9C70
- `RegisterTag`: 0x14031AC90
- `GetTagPos`: 0x1400FBDE0
- And many more...

## 🚀 Getting Started

### Prerequisites
- Visual Studio 2019 or later
- Windows SDK
- DirectX SDK (for D3D9 headers)
- Basic knowledge of C++ and game hacking concepts

### Building the Framework

1. Create a new DLL project in Visual Studio
2. Add the framework files to your project:
   - `BO3_ModFramework.h`
   - `BO3_ModFramework.cpp`
   - `ExampleMod.cpp`
3. Link against required libraries:
   - `d3d9.lib`
   - `d3dx9.lib`
4. Compile as a 64-bit DLL

### Basic Usage Example

```cpp
#include "BO3_ModFramework.h"

void MyModUpdate() {
    // Check if we're in a game
    if (!IsInGame()) return;
    
    // Get local player info
    CClientInfo* localPlayer = GetClientByIndex(0);
    if (!localPlayer) return;
    
    // Loop through all players
    for (int i = 1; i < 64; i++) {
        CEntity* entity = CG_GetEntity(0, i);
        if (!IsValidEntity(entity)) continue;
        
        CClientInfo* client = GetClientByIndex(i);
        if (!client) continue;
        
        // Check if enemy
        if (IsEnemy(client, localPlayer->iTeam)) {
            // Get player position
            Vec3 playerPos = entity->vOrigin;
            
            // Convert to screen coordinates
            Vec2 screenPos;
            if (WorldToScreen(playerPos, screenPos)) {
                // Draw something at screen position
                char text[64];
                sprintf_s(text, "%s", client->szName);
                DrawText(text, screenPos.x, screenPos.y, 1.0f, 1.0f);
            }
        }
    }
}
```

## 🎯 Available Functions

### Entity Management
- `CG_GetEntity(int a1, int idx)` - Get entity by index
- `IsValidEntity(CEntity* entity)` - Validate entity
- `GetDistance(Vec3 pos1, Vec3 pos2)` - Calculate distance

### Player Information
- `GetClientByIndex(int idx)` - Get client info
- `IsEnemy(CClientInfo* client, int localTeam)` - Team check
- `GetCurrentWeaponName()` - Get weapon name

### Rendering
- `DrawText(char* text, float x, float y, float sx, float sy)` - Draw text
- `WorldToScreen(Vec3 worldPos, Vec2& screenPos)` - 3D to 2D conversion
- `GetShader(char* shaderName)` - Get game shader

### Bone/Tag System
- `GetBonePosition(CEntity* entity, const char* boneName)` - Get bone position
- `RegisterTag(char* boneName)` - Register bone tag

### Game State
- `IsInGame()` - Check if in active game
- `GetGameInfo(char* gameType, char* mapName, char* hostName)` - Get game info

## 🦴 Bone Names Reference

Common bone names for skeleton ESP:
- `j_head` - Head
- `j_neck` - Neck
- `j_shoulder_le/ri` - Left/Right shoulder
- `j_elbow_le/ri` - Left/Right elbow
- `j_wrist_le/ri` - Left/Right wrist
- `j_spinelower` - Lower spine
- `j_knee_le/ri` - Left/Right knee
- `j_ankle_le/ri` - Left/Right ankle

## 🏴‍☠️ Entity Flags

```cpp
enum EFlags {
    EF_STANDING     = 0x00000002,
    EF_CROUCHING    = 0x00000004,
    EF_PRONE        = 0x00000008,
    EF_FIRING       = 0x00000040,
    EF_SPRINTING    = 0x00002000,
    EF_DEAD         = 0x00004000,
    EF_ADS          = 0x00008000  // Aiming down sights
};
```

## 🔄 Version Compatibility

This framework is designed for **Call of Duty: Black Ops 3 version **********. 

**Important:** Game updates will change memory addresses, requiring updates to the framework. Always verify your game version before using.

## 🛠️ Extending the Framework

### Adding New Features

1. **Custom ESP Elements**
   ```cpp
   void DrawCustomESP(CEntity* entity) {
       // Your custom ESP code here
   }
   ```

2. **New Game Data Access**
   ```cpp
   // Add new function signatures to header
   typedef YourStruct* (*GetYourData_)(int param);
   extern GetYourData_ GetYourData;
   
   // Add address in cpp file
   GetYourData_ GetYourData = (GetYourData_)0xYourAddress;
   ```

3. **Additional Utilities**
   ```cpp
   bool IsPlayerVisible(CEntity* entity) {
       // Implement visibility check
       return true;
   }
   ```

## 📚 Learning Resources

- **UnknownCheats Forum**: Primary source for reverse engineering info
- **Cheat Engine**: For memory analysis and debugging
- **IDA Pro/Ghidra**: For static analysis
- **x64dbg**: For dynamic analysis

## 🤝 Contributing

This framework is based on community research. Contributions are welcome:
1. Update memory addresses for new game versions
2. Add new game structures
3. Implement additional utility functions
4. Improve documentation

## 📄 Credits

- **UnknownCheats Community**: For reverse engineering research
- **Ælius, master131, cra0, maxkunes, Kenshin13**: Original researchers
- **Kozmo, Armadillo, Skyfail**: Additional contributors

## ⚖️ Legal Notice

This software is provided for educational purposes only. The authors are not responsible for any misuse or consequences resulting from the use of this framework. Always respect game terms of service and applicable laws.
