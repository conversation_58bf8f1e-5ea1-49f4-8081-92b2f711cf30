@echo off
echo Building BO3 Mod Framework GUI Components...
echo.

REM Build the launcher first
echo Building launcher...
g++ -o BO3_Launcher.exe BO3_Launcher.cpp -static-libgcc -static-libstdc++
if errorlevel 1 (
    echo Failed to build launcher
    pause
    exit /b 1
)
echo ✓ Launcher built successfully

REM Build the GUI
echo Building GUI...
g++ -o BO3_GUI.exe BO3_GUI.cpp -lcomctl32 -lgdi32 -luser32 -lkernel32 -static-libgcc -static-libstdc++
if errorlevel 1 (
    echo Failed to build GUI
    pause
    exit /b 1
)
echo ✓ GUI built successfully

echo.
echo All GUI components built successfully!
echo.
echo You can now run:
echo - BO3_Launcher.exe (Main launcher with menu)
echo - BO3_GUI.exe (Direct GUI access)
echo.
pause
