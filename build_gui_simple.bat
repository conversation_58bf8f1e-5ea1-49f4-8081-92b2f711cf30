@echo off
echo Building BO3 GUI with MSBuild...
echo.

set MSBUILD="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"

if not exist %MSBUILD% (
    echo MSBuild not found at expected location!
    echo Please check Visual Studio 2022 installation.
    pause
    exit /b 1
)

echo Found MSBuild at: %MSBUILD%
echo.

echo Building BO3_GUI.sln...
%MSBUILD% BO3_GUI.sln /p:Configuration=Release /p:Platform=x64 /v:minimal

if errorlevel 1 (
    echo.
    echo Build failed! Check the error messages above.
    pause
    exit /b 1
)

echo.
echo Build successful!
echo.

if exist "bin\Release\BO3_GUI.exe" (
    echo ✓ BO3_GUI.exe created successfully!
    echo.
    echo Starting BO3_GUI...
    start bin\Release\BO3_GUI.exe
) else (
    echo ❌ BO3_GUI.exe not found in expected location.
    echo Checking for executable...
    dir /s BO3_GUI.exe
)

echo.
pause
