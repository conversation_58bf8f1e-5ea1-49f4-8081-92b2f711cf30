# Makefile for BO3 Mod Framework
# Requires MinGW-w64 or similar GCC toolchain for Windows

CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2 -m64
DEFINES = -DWIN32_LEAN_AND_MEAN -DNOMINMAX -D_CRT_SECURE_NO_WARNINGS
INCLUDES = -I.
LIBS = -ld3d9 -ld3dx9 -lkernel32 -luser32 -lgdi32 -ladvapi32 -lshell32

# Directories
BUILD_DIR = build
BIN_DIR = $(BUILD_DIR)/bin
LIB_DIR = $(BUILD_DIR)/lib
OBJ_DIR = $(BUILD_DIR)/obj

# Source files
FRAMEWORK_SOURCES = BO3_ModFramework.cpp
EXAMPLE_MOD_SOURCES = ExampleMod.cpp
INJECTOR_SOURCES = Injector.cpp
TEST_SOURCES = test_build.cpp

# Object files
FRAMEWORK_OBJS = $(FRAMEWORK_SOURCES:%.cpp=$(OBJ_DIR)/%.o)
EXAMPLE_MOD_OBJS = $(EXAMPLE_MOD_SOURCES:%.cpp=$(OBJ_DIR)/%.o)
INJECTOR_OBJS = $(INJECTOR_SOURCES:%.cpp=$(OBJ_DIR)/%.o)
TEST_OBJS = $(TEST_SOURCES:%.cpp=$(OBJ_DIR)/%.o)

# Targets
FRAMEWORK_LIB = $(LIB_DIR)/libBO3ModFramework.a
EXAMPLE_MOD_DLL = $(BIN_DIR)/BO3ExampleMod.dll
INJECTOR_EXE = $(BIN_DIR)/BO3Injector.exe
TEST_EXE = $(BIN_DIR)/BO3BuildTest.exe

# Default target
all: directories $(FRAMEWORK_LIB) $(EXAMPLE_MOD_DLL) $(INJECTOR_EXE) $(TEST_EXE)

# Create directories
directories:
	@mkdir -p $(BUILD_DIR) $(BIN_DIR) $(LIB_DIR) $(OBJ_DIR)

# Framework static library
$(FRAMEWORK_LIB): $(FRAMEWORK_OBJS)
	@echo "Creating framework library..."
	ar rcs $@ $^

# Example mod DLL
$(EXAMPLE_MOD_DLL): $(EXAMPLE_MOD_OBJS) $(FRAMEWORK_LIB)
	@echo "Building example mod DLL..."
	$(CXX) -shared -o $@ $^ $(LIBS) -Wl,--out-implib,$(LIB_DIR)/libBO3ExampleMod.dll.a

# Injector executable
$(INJECTOR_EXE): $(INJECTOR_OBJS)
	@echo "Building injector executable..."
	$(CXX) -o $@ $^ $(LIBS)

# Test executable
$(TEST_EXE): $(TEST_OBJS) $(FRAMEWORK_LIB)
	@echo "Building test executable..."
	$(CXX) -o $@ $^ $(LIBS)

# Object file compilation
$(OBJ_DIR)/%.o: %.cpp
	@echo "Compiling $<..."
	$(CXX) $(CXXFLAGS) $(DEFINES) $(INCLUDES) -c $< -o $@

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	rm -rf $(BUILD_DIR)

# Install (copy to package directory)
install: all
	@echo "Creating package..."
	@mkdir -p package
	cp $(EXAMPLE_MOD_DLL) package/
	cp $(INJECTOR_EXE) package/
	cp $(TEST_EXE) package/
	cp README.md package/
	cp TECHNICAL_DETAILS.md package/
	cp BO3_ModFramework.h package/
	@echo "Package created in 'package' directory"

# Test build
test: $(TEST_EXE)
	@echo "Running build test..."
	$(TEST_EXE)

# Help
help:
	@echo "BO3 Mod Framework Build System"
	@echo ""
	@echo "Targets:"
	@echo "  all       - Build all components (default)"
	@echo "  clean     - Remove build artifacts"
	@echo "  install   - Create distribution package"
	@echo "  test      - Build and run test executable"
	@echo "  help      - Show this help message"
	@echo ""
	@echo "Requirements:"
	@echo "  - MinGW-w64 GCC toolchain"
	@echo "  - DirectX SDK (for d3d9 headers)"
	@echo "  - Windows environment"
	@echo ""
	@echo "Usage:"
	@echo "  make all      # Build everything"
	@echo "  make clean    # Clean build"
	@echo "  make install  # Create package"

.PHONY: all directories clean install test help
