#pragma once
#ifndef BO3_ADVANCED_EXTENSIONS_H
#define BO3_ADVANCED_EXTENSIONS_H

#include "BO3_ModFramework.h"

// ========== ADVANCED STRUCTURES FROM LATEST RESEARCH ==========

// Enhanced Entity State (from <PERSON><PERSON><PERSON>'s research)
typedef struct {
    int32_t iNumber;                // 0x0000
    char eFlags;                    // 0x0004
    char pad_0004[8];              // 0x0008
    int32_t eType2;                // 0x0010
    char pad_0014[8];              // 0x0014
    Vec3 vOrigin;                  // 0x001C
    char pad_0028[376];            // 0x0028
    int32_t eType;                 // 0x01A0
    char pad_01A4[20];             // 0x01A4
    int32_t iWeaponID;             // 0x01B8
    char pad_01BC[844];            // 0x01BC
} entityState_t;                   // Size: 0x0508

// Enhanced Player State
typedef struct {
    char pad_0000[48];             // 0x0000
    Vec3 vOrigin;                  // 0x0030
    Vec3 vVelocity;                // 0x003C
    char pad_0048[136];            // 0x0048
    Vec3 angle_t;                  // 0x00D0
    char pad_00DC[572];            // 0x00DC
    float fPitch;                  // 0x0318
    float fYaw;                    // 0x031C
} playerState_t;                   // Size: 0x0320

// Enhanced RefDef
typedef struct {
    char pad_0000[12];             // 0x0000
    int32_t iScreenX;              // 0x000C
    int32_t iScreenY;              // 0x0010
    char pad_0014[100];            // 0x0014
    Vec2 N001B6807;                // 0x0078
    char pad_0080[8];              // 0x0080
    Vec3 vViewOrigin;              // 0x0088
    char pad_0094[16];             // 0x0094
    Vec3 vViewAxis[3];             // 0x00A4
    char pad_00C8[56];             // 0x00C8
} refDef_t;                        // Size: 0x0100

// Enhanced Client Info
typedef struct {
    int32_t iInfoValid;            // 0x0000
    int32_t iNextValid;            // 0x0004
    int32_t iNumber;               // 0x0008
    char cName[32];                // 0x000C
    int32_t iTeam;                 // 0x002C
    int32_t iTeam2;                // 0x0030
    char pad_0034[76];             // 0x0034
    char cClanTag[4];              // 0x0080
    char pad_0084[8];              // 0x0084
    int32_t iAlive;                // 0x008C (0 = Alive, 2 = Dead)
    char pad_0090[100];            // 0x0090
    int32_t iHealth;               // 0x00F4
    char pad_00F8[3544];           // 0x00F8
} clientInfo_t;                    // Size: 0x0ED0

// Complete CEntity
typedef struct {
    char pad_0000[64];             // 0x0000
    Vec3 vOrigin;                  // 0x0040
    Vec3 vViewAngles;              // 0x004C
    char pad_0058[740];            // 0x0058
    Vec3 vOrigin2;                 // 0x033C
    char pad_0348[48];             // 0x0348
    int64_t iWeaponID;             // 0x0378
    char pad_0380[120];            // 0x0380
    entityState_t nextState;       // 0x03F8
} centity_t;                       // Size: 0x0900

// User Command Structure
typedef struct {
    int iServerTime;               // 0x0000
    int iButtons;                  // 0x0004
    char pad_0x0008[0x8];          // 0x0008
    int iViewAngles[3];            // 0x0010
    char pad_0x001C[0x4];          // 0x001C
    __int64 iWeapon;               // 0x0020
    char pad_0x0028[0x10];         // 0x0028
    signed char cForwardmove;      // 0x0038
    signed char cSidemove;         // 0x0039
    char pad_0x003A[0x16];         // 0x003A
} usercmd_s;

// Client Active State
typedef struct {
    char pad_0000[224];            // 0x0000
    Vec3 vOrigin;                  // 0x00E0
    char pad_00EC[148];            // 0x00EC
    Vec3 vSpawnAngles;             // 0x0180
    char pad_018C[46908];          // 0x018C
    Vec3 vViewAngles;              // 0xB8C8
    char pad_B8D4[1048604];        // 0xB8D4
    usercmd_s cmds[128];           // 0x10B8F0
    int32_t iCmdNumber;            // 0x10E0F0
} clientActive_t;

// Collision Context
typedef struct {
    int mask;
    int* prims;
    int nprims;
    int* ignoreEntParams;
    int passEntityNum0;
    int passEntityNum1;
    int staticmodels;
    int locational;
    char* priorityMap;
    int(__cdecl* collide_entity_func)(int);
} col_context_t;

// Trace Result
typedef struct {
    char pad[0x10];
    float fraction;                // 0x0010
    char padding[0x256];
} trace_t;

// Complete CG_T Structure
typedef struct {
    int64_t iClientNum;            // 0x0000
    int64_t iLocalClientNum;       // 0x0008
    char pad_0010[24];             // 0x0010
    void* pCurrentSnap;            // 0x0028
    void* pNextSnap;               // 0x0030
    char pad_0038[72];             // 0x0038
    Vec3 vOrigin;                  // 0x0080
    Vec3 vVelocity;                // 0x008C
    char pad_0098[1157144];        // 0x0098
    playerState_t playerState;     // 0x11A8B0
    char pad_11ABD0[2136];         // 0x11ABD0
    float fAimSpreadScale;         // 0x11B428
    char pad_11B42C[92356];        // 0x11B42C
    refDef_t refdef;               // 0x131CF0
    char pad_131DF0[1793104];      // 0x131DF0
    clientInfo_t clientInfo[18];   // 0x2E7A40
    char pad_2F84E0[5973504];      // 0x2F84E0
    centity_t centityArray[18];    // 0x8AAAE0
    char pad_8B4CE0[5217560];      // 0x8B4CE0
} cg_t;                            // Size: 0xDAE9F8

// ========== ADVANCED FUNCTION SIGNATURES ==========

// Collision and Tracing
typedef int (*CG_Trace_)(trace_t* trace, Vec3* start, Vec3* mins, Vec3* maxs, Vec3* end, int skipEntityNum, int mask);
typedef int (*CM_BoxTrace_)(trace_t* trace, Vec3* start, Vec3* end, Vec3* mins, Vec3* maxs, int model, int brushmask);

// User Command Functions
typedef usercmd_s* (*CL_GetUserCmd_)(int cmdNumber);
typedef void (*CL_SetUserCmdValue_)(int userCmdValue, float sensitivityScale, float mPitchOverride, float mYawOverride, float mForwardOverride, float mSideOverride);

// Client Active Functions
typedef clientActive_t* (*CL_GetClientActive_)();

// Advanced Entity Functions
typedef centity_t* (*CG_GetEntity_Advanced_)(int localClientNum, int entityNum);
typedef entityState_t* (*CG_GetEntityState_)(int localClientNum, int entityNum);

// Prediction Functions
typedef void (*CG_PredictPlayerState_)();
typedef Vec3 (*CG_PredictPlayerMovement_)(Vec3 currentPos, Vec3 velocity, float deltaTime);

// Weapon Functions
typedef int (*CG_GetWeaponIndexForName_)(const char* weaponName);
typedef const char* (*CG_GetWeaponNameForIndex_)(int weaponIndex);

// View Angle Functions
typedef void (*CG_SetViewAngles_)(Vec3* angles);
typedef Vec3* (*CG_GetViewAngles_)();

// ========== ADVANCED UTILITIES ==========

// Velocity-based prediction
Vec3 PredictEntityPosition(centity_t* entity, float deltaTime);

// Advanced collision detection
bool IsPositionVisible(Vec3 start, Vec3 end, int ignoreEntity);
bool IsEntityVisible(centity_t* entity, Vec3 viewOrigin);

// Movement prediction
Vec3 PredictPlayerMovement(Vec3 currentPos, Vec3 velocity, float deltaTime);

// Advanced aimbot calculations
Vec3 CalculateInterceptionPoint(Vec3 targetPos, Vec3 targetVel, Vec3 shooterPos, float projectileSpeed);
float CalculateTimeToTarget(Vec3 start, Vec3 end, float projectileSpeed);

// User command manipulation
void ModifyUserCommand(usercmd_s* cmd, Vec3 desiredAngles, int buttons);
void SilentAim(usercmd_s* cmd, Vec3 targetPos, Vec3 shooterPos);

// Advanced ESP calculations
bool IsEntityBehindWall(centity_t* entity, Vec3 viewOrigin);
float GetEntityDistance3D(centity_t* entity, Vec3 viewOrigin);
Vec3 GetEntityVelocity(centity_t* entity);

// Clan tag and advanced info
const char* GetPlayerClanTag(int clientNum);
int GetPlayerHealth(int clientNum);
bool IsPlayerAlive(int clientNum);

// Advanced team detection
bool IsSpectator(int clientNum);
bool IsSameTeam(int clientNum1, int clientNum2);

// Weapon state detection
bool IsPlayerReloading(int clientNum);
bool IsPlayerADS(int clientNum);
bool IsPlayerSprinting(int clientNum);

// Movement state detection
bool IsPlayerOnGround(int clientNum);
bool IsPlayerJumping(int clientNum);
bool IsPlayerCrouching(int clientNum);
bool IsPlayerProne(int clientNum);

// Advanced math utilities
float VectorLength(Vec3 vec);
Vec3 VectorNormalize(Vec3 vec);
Vec3 VectorSubtract(Vec3 a, Vec3 b);
Vec3 VectorAdd(Vec3 a, Vec3 b);
Vec3 VectorScale(Vec3 vec, float scale);
float VectorDotProduct(Vec3 a, Vec3 b);
Vec3 VectorCrossProduct(Vec3 a, Vec3 b);

// Angle utilities
Vec3 AnglesToVector(Vec3 angles);
Vec3 VectorToAngles(Vec3 vector);
float AngleDifference(float angle1, float angle2);
Vec3 NormalizeAngles(Vec3 angles);

// Screen utilities
bool WorldToScreenAdvanced(Vec3 worldPos, Vec2* screenPos, refDef_t* refdef);
Vec3 ScreenToWorld(Vec2 screenPos, float depth, refDef_t* refdef);

// Memory utilities
bool IsValidPointer(void* ptr);
bool IsValidEntity(centity_t* entity);
bool IsValidClientInfo(clientInfo_t* client);

// Performance utilities
void CacheEntityData(int entityNum);
void InvalidateEntityCache(int entityNum);
bool IsEntityCached(int entityNum);

// ========== ADVANCED CONSTANTS ==========

// Button flags for usercmd_s
#define BUTTON_ATTACK       0x00000001
#define BUTTON_JUMP         0x00000002
#define BUTTON_CROUCH       0x00000004
#define BUTTON_PRONE        0x00000008
#define BUTTON_ADS          0x00000010
#define BUTTON_RELOAD       0x00000020
#define BUTTON_SPRINT       0x00000040
#define BUTTON_MELEE        0x00000080
#define BUTTON_GRENADE      0x00000100
#define BUTTON_TACTICAL     0x00000200

// Trace masks
#define MASK_ALL            0xFFFFFFFF
#define MASK_SOLID          0x00000001
#define MASK_PLAYERSOLID    0x00000002
#define MASK_SHOT           0x00000004
#define MASK_WATER          0x00000008

// Entity types
#define ET_GENERAL          0
#define ET_PLAYER           1
#define ET_ITEM             2
#define ET_MISSILE          3
#define ET_INVISIBLE        4
#define ET_SCRIPTMOVER      5

// Team numbers
#define TEAM_SPECTATOR      0
#define TEAM_ALLIES         1
#define TEAM_AXIS           2

#endif // BO3_ADVANCED_EXTENSIONS_H
