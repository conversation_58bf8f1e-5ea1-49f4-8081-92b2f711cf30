# How to Build and Run the BO3 GUI

## 🚨 **Terminal Environment Issue**
The current terminal environment has some compatibility issues with Windows development tools. Here's how to build and run the GUI manually:

## 🛠️ **Method 1: Using Visual Studio Developer Command Prompt**

### **Step 1: Open Developer Command Prompt**
1. Press `Win + R`
2. Type: `cmd`
3. Navigate to your BLOPS3 directory:
   ```cmd
   cd "C:\Users\<USER>\Desktop\BLOPS3"
   ```
4. Initialize Visual Studio environment:
   ```cmd
   "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
   ```

### **Step 2: Build the Components**
```cmd
REM Build the launcher
cl /EHsc BO3_Launcher.cpp /Fe:BO3_Launcher.exe

REM Build the GUI
cl /EHsc BO3_GUI.cpp /link comctl32.lib gdi32.lib user32.lib kernel32.lib /Fe:BO3_GUI.exe

REM Build the framework library
cl /c BO3_ModFramework.cpp
cl /c BO3_AdvancedExtensions.cpp
lib BO3_ModFramework.obj BO3_AdvancedExtensions.obj /OUT:BO3ModFramework.lib

REM Build the example mod DLL
cl /LD ExampleMod.cpp BO3ModFramework.lib /Fe:BO3ExampleMod.dll

REM Build the advanced mod DLL  
cl /LD AdvancedExampleMod.cpp BO3ModFramework.lib /Fe:BO3AdvancedMod.dll

REM Build the injector
cl /EHsc Injector.cpp /Fe:BO3Injector.exe
```

### **Step 3: Run the GUI**
```cmd
BO3_GUI.exe
```

## 🛠️ **Method 2: Using the Batch Files**

### **Step 1: Open Command Prompt**
1. Press `Win + R`
2. Type: `cmd`
3. Navigate to BLOPS3 directory:
   ```cmd
   cd "C:\Users\<USER>\Desktop\BLOPS3"
   ```

### **Step 2: Run Build Script**
```cmd
build_gui.bat
```

### **Step 3: Launch**
```cmd
BO3_GUI.exe
```

## 🛠️ **Method 3: Using Visual Studio IDE**

### **Step 1: Create New Project**
1. Open Visual Studio 2022
2. Create new "Windows Desktop Application" project
3. Add all the .cpp and .h files to the project

### **Step 2: Configure Project**
1. Right-click project → Properties
2. Configuration Properties → Linker → Input
3. Add Additional Dependencies:
   - `comctl32.lib`
   - `gdi32.lib`
   - `user32.lib`
   - `kernel32.lib`

### **Step 3: Build and Run**
1. Press F5 or Build → Build Solution
2. Run the generated executable

## 🖥️ **What the GUI Looks Like**

When successfully built and run, you'll see this interface:

```
┌─────────────────────────────────────────────────────────────────┐
│ Call of Duty: Black Ops 3 - Mod Control Panel            [_][□][X]│
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ Target Process: [BlackOps3.exe (PID: 1234) ▼] [Refresh]       │
│                                                                 │
│ DLL Path: [C:\...\BO3ExampleMod.dll        ] [Browse] [Inject] │
│                                                        [Eject ] │
│                                                                 │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ Basic Features:              │ Advanced Features:               │
│ ☐ ESP (Player Highlighting) │ ☐ Predictive ESP                │
│ ☐ Aimbot Assistance         │ ☐ Radar Hack                    │
│ ☐ Information Display       │ ☐ Trigger Bot                   │
│                              │ ☐ Wall Hack ESP                 │
│                              │ ☐ Health ESP                    │
│                              │ ☐ Clan Tag ESP                  │
│                                                                 │
├─────────────────────────────────────────────────────────────────┤
│ ⚠️ WARNING: Educational use only! Never use online!            │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ Activity Log:                                                   │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │[12:34:56] BO3 Mod Control Panel initialized.              │ │
│ │[12:35:01] Process list refreshed                           │ │
│ │[12:35:05] BlackOps3.exe found (PID: 1234)                 │ │
│ │[12:35:10] DLL injected successfully!                       │ │
│ │[12:35:15] ✅ ESP toggled                                   │ │
│ │[12:35:20] 🎯 Aimbot toggled                                │ │
│ │[12:35:25] 📊 Info display toggled                          │ │
│ │                                                            │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
├─────────────────────────────────────────────────────────────────┤
│ Hotkeys: F1=ESP, F2=Aimbot, F3=Info, F4=Advanced ESP, F5=Radar │
│ Status: Injected and ready                                      │
└─────────────────────────────────────────────────────────────────┘
```

## 🎮 **GUI Features**

### **Process Management**
- **Dropdown List**: Shows all running processes
- **Auto-Detection**: Automatically finds BlackOps3.exe
- **Refresh Button**: Updates process list
- **PID Display**: Shows process ID for verification

### **DLL Management**
- **File Browser**: Easy DLL selection
- **Path Display**: Shows current DLL path
- **Inject Button**: One-click injection
- **Eject Button**: Safe DLL removal

### **Feature Controls**
- **Checkboxes**: Toggle features on/off
- **Real-time**: Changes apply immediately
- **Visual Feedback**: Checkmarks show active features
- **Grouped Layout**: Basic vs Advanced features

### **Activity Log**
- **Timestamped**: All actions logged with time
- **Color Coded**: Different message types
- **Scrollable**: Full history available
- **Status Updates**: Real-time feedback

### **Safety Features**
- **Warning Messages**: Educational reminders
- **Status Bar**: Current injection status
- **Error Handling**: Graceful failure recovery
- **Hotkey Display**: Always visible controls

## 🚀 **Usage Workflow**

### **Step 1: Preparation**
1. Build all components using one of the methods above
2. Start Black Ops 3 in offline mode
3. Launch the GUI

### **Step 2: Injection**
1. Click "Refresh" to find BlackOps3.exe
2. Select the process from dropdown
3. Browse for your mod DLL if needed
4. Click "Inject DLL"
5. Wait for "✅ DLL injected successfully!" message

### **Step 3: Control Features**
1. Check boxes to enable features:
   - ☑️ ESP for player highlighting
   - ☑️ Aimbot for targeting assistance
   - ☑️ Info Display for game stats
   - ☑️ Advanced features as desired

### **Step 4: Monitor**
- Watch activity log for status updates
- Use hotkeys (F1-F6) for quick control
- Check status bar for current state

### **Step 5: Cleanup**
1. Uncheck all features
2. Click "Eject DLL"
3. Close GUI
4. Exit Black Ops 3

## ⚠️ **Important Notes**

- **Educational Only**: Framework is for learning purposes
- **Offline Only**: Never use in online multiplayer
- **Version Specific**: Only works with BO3 version ********
- **Administrator**: May need to run as Administrator
- **Antivirus**: May need to whitelist the executables

The GUI provides a complete, user-friendly interface for the BO3 modding framework while maintaining strong educational focus and safety warnings!
