#include <Windows.h>

// Simple test mod that just shows a message box
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        // Show a big, obvious message box
        MessageBoxA(nullptr, 
                   "SUCCESS!\n\n"
                   "The BO3 Educational Mod has been injected!\n\n"
                   "This proves DLL injection is working.\n\n"
                   "Press F1 in the game to see another message.\n\n"
                   "This is for educational purposes only!",
                   "BO3 MOD INJECTED SUCCESSFULLY!",
                   MB_OK | MB_ICONINFORMATION | MB_TOPMOST);
        
        // Create a thread to handle hotkeys
        CreateThread(nullptr, 0, [](LPVOID) -> DWORD {
            while (true) {
                if (GetAsyncKeyState(VK_F1) & 0x8000) {
                    MessageBoxA(nullptr,
                               "F1 Key Detected!\n\n"
                               "The mod is responding to your input.\n\n"
                               "This demonstrates that the educational\n"
                               "mod framework is working correctly.\n\n"
                               "Try F2 or F3 for other features!",
                               "ESP Feature Activated!",
                               MB_OK | MB_ICONEXCLAMATION | MB_TOPMOST);
                    Sleep(500); // Debounce
                }
                
                if (GetAsyncKeyState(VK_F2) & 0x8000) {
                    MessageBoxA(nullptr,
                               "F2 Key Detected!\n\n"
                               "Aimbot feature would be activated here.\n\n"
                               "This is just a demonstration of how\n"
                               "game mods can respond to hotkeys.\n\n"
                               "Educational purposes only!",
                               "Aimbot Feature Activated!",
                               MB_OK | MB_ICONWARNING | MB_TOPMOST);
                    Sleep(500); // Debounce
                }
                
                if (GetAsyncKeyState(VK_F3) & 0x8000) {
                    MessageBoxA(nullptr,
                               "F3 Key Detected!\n\n"
                               "Info display feature activated.\n\n"
                               "In a real mod, this would show\n"
                               "game information overlays.\n\n"
                               "This is educational only!",
                               "Info Display Activated!",
                               MB_OK | MB_ICONINFORMATION | MB_TOPMOST);
                    Sleep(500); // Debounce
                }
                
                if (GetAsyncKeyState(VK_ESCAPE) & 0x8000) {
                    MessageBoxA(nullptr,
                               "ESC Key Detected!\n\n"
                               "The educational mod is now exiting.\n\n"
                               "Thank you for testing the framework!",
                               "Mod Exiting...",
                               MB_OK | MB_ICONINFORMATION | MB_TOPMOST);
                    ExitThread(0);
                }
                
                Sleep(50); // Check every 50ms
            }
            return 0;
        }, nullptr, 0, nullptr);
        break;
        
    case DLL_PROCESS_DETACH:
        MessageBoxA(nullptr,
                   "The BO3 Educational Mod has been unloaded.\n\n"
                   "Thank you for testing!",
                   "Mod Unloaded",
                   MB_OK | MB_ICONINFORMATION | MB_TOPMOST);
        break;
    }
    return TRUE;
}
